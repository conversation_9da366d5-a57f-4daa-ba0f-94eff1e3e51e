<template>
  <div class="main-wrapper">
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <div style="display: flex;align-items: center">
          <el-image src="/icon/16.png" class="icon"></el-image>
          <el-tooltip placement="top" content="点击修改试卷名称">
            <div @click="onEditName(file?.id, file?.name)" class="text"
              style="text-decoration: underline;cursor: pointer;">{{ file?.name }}
            </div>
          </el-tooltip>
          <el-tooltip placement="top" content="点击复制试卷名称">
            <el-button style="margin-left: 5px" type="text" icon="CopyDocument"
              @click="copyPaperName(file?.name)"></el-button>
          </el-tooltip>

          <el-tooltip placement="top" content="点击修改班级">
            <div style="margin-left: 5px;text-decoration: underline;cursor: pointer;" @click="openClassChangeDialog"
              class="text">
              {{ allTaskData?.school?.schoolName || '' }}
              班级：{{ allTaskData?.class?.className || '无' }}
            </div>
          </el-tooltip>

          <!--          <el-input class="filter-item" v-model="filter.name" placeholder="请输入名称"/>-->
          <!--          <el-select class="filter-item" v-model="filter.status" placeholder="请选择状态" clearable>-->
          <!--            <el-option v-for="item in Object.values(statusOptions)" :key="item.value" :label="item.label"-->
          <!--                       :value="item.value"/>-->
          <!--          </el-select>-->
          <!--          <el-button type="primary" @click="loadData">搜索</el-button>-->

          <div style="display: flex;align-items: center">
            <div style="text-decoration: underline;cursor: pointer;margin: 0 0" class="text">备注：</div>
            <el-tooltip placement="top" content="点击修改备注">
              <div @click="editRemark(allTaskData?.fileDetail.id, allTaskData?.fileDetail.remark)"
                style="text-decoration: underline;cursor: pointer;margin: 0 0" class="text">
                {{ allTaskData?.fileDetail?.remark || '点击填写' }}
              </div>
            </el-tooltip>

            <el-tooltip placement="top" content="点击复制备注">
              <el-button style="margin-left: 10px" type="text" icon="CopyDocument"
                @click="copyRemark(allTaskData.fileDetail.remark)"></el-button>
            </el-tooltip>
          </div>

          <div style="display: flex;align-items: center;font-size: 16px">
            <div style="margin-left: 10px;font-weight: bold;">批改速度：</div>
            <div style="color: #E74C3C;font-size: 20px;font-weight: bold;">{{ rpm }}</div>
            <div style="margin-left: 5px;font-weight: bold;">题/分钟</div>
          </div>
          <el-tooltip placement="top" content="点击查看详情">
            <div @click="fetchDetailAndShow" style="margin-left: 5px;text-decoration: underline;cursor: pointer;"
              class="text">
              详情
            </div>
          </el-tooltip>
          <div v-if="convertIdx" style="display: flex;align-items: center">
            <div style="margin-left: 5px;font-size: 12px;color: #606266;cursor: pointer;text-decoration: underline;"
              @click="closeConvertpdf2Img">图片已缓存：{{ convertIdx }} / {{ paperSize }}
            </div>
          </div>

        </div>
        <el-dialog v-model="downloading" title="批量下载" :close-on-click-modal="false" :show-close="false" width="350px">
          <div style="margin-bottom: 10px;">{{ downloadStatus }}</div>
          <el-progress
            :percentage="downloadTotal ? Math.round(downloadProgress / downloadTotal * 100) : 0"></el-progress>
        </el-dialog>
      </template>
      <template #extra>
        <div>
          <el-button class="header-action" type="primary" icon="Printer" @click="openPrintDialog">保存结果
          </el-button>
          <!--          <el-button class="header-action" type="primary" icon="FolderOpened" v-if="corrected"-->
          <!--                     @click="dialogVisible = true, remoteFileSaveCheckList = ['correctionDoesNotIncludeTheOriginalPaper', 'correctionIncludeTheOriginalPaper', 'statisticalResults']">-->
          <!--            保存到远程文件夹-->
          <!--          </el-button>-->
          <!--          <el-button class="header-action" type="primary" icon="Printer" v-if="corrected"-->
          <!--                     @click="$refs.remoteFilePrintSelectorRef.show()">-->
          <!--            远程打印-->
          <!--          </el-button>-->
          <el-button class="header-action" type="primary" icon="Download"
            @click="$refs.chooseExportExcelTypeDialog.show()">导出表格
          </el-button>
          <!--          <el-button class="header-action" type="primary" icon="RefreshRight" @click="initCropper">刷新预览</el-button>-->
          <el-button class="header-action" type="primary" icon="Check" @click="finishCropper(true)">完成</el-button>
          <el-button class="header-action" type="primary" icon="RefreshRight" @click="refresh"
            :loading="taskLoading">刷新</el-button>
        </div>
      </template>
    </el-page-header>
    <!--    <el-image src="/icon/paperStep3.svg" class="step" v-if="corrected"></el-image>-->
    <!--    <el-image src="/icon/paperStep2.svg" class="step" v-else></el-image>-->
    <div class="main-content">
      <div class="left-content" :style="{ width: (boxWidth - 10) + 'px' }">
        <el-empty description="加载中..." v-if="tasks.length === 0" />
        <el-table v-else v-loading="taskLoading" :data="tasks" style="height: 100%;table-layout: fixed;"
          empty-text="无数据" border :expand-row-keys="expandRows" row-key="id" default-expand-all
          @expand-change="onExpandChange">
          <el-table-column v-for="column in columns" :key="column.prop" v-bind="column" align="center"
            :width="column.width || ''">
            <el-table-column type="expand" width="60" align="left">
              <template #default="props">
                <div style="width: 100%;display: flex;justify-content: flex-start">
                  <record :ref="props.row.id" :task-id="props.row.id" :isCorrected="corrected"
                    :select-row-index="nowCropper.paperIdx" :is-task-selected="nowCropper.taskId === props.row.id"
                    :parent-loaded-data="allTaskData?.[props.row.id]" :students="students"
                    :task-size="allTaskData?.tasks?.length" :file-id="fileId" :paper-name="file?.name" show-student-name
                    show-student-number @swap="swap" @review="reviewTask"
                    @errorCorrectionFromTable="errorCorrectionFromTable" @refresh="refresh"
                    @refreshRecordNameOrStudentNumber="refreshRecordNameOrStudentNumber"
                    @bath-edit-stu-No="bacthEditStuNo"
                    @bath-all-wrong-or-right="bathAllWrongOrRight($event, props.$index)" @ration="rationConfirm" />
                </div>
              </template>
            </el-table-column>
            <template v-if="column.prop === 'operations'" v-slot="scope">
              <el-space :size="5" style="display: flex;flex-wrap: wrap;row-gap: 10px;justify-content: center">
                <el-dropdown>
                  <span>
                    <el-icon class="el-icon--right">
                      <more />
                    </el-icon>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item v-if="scope.row.status !== 2" @click="correctTask(scope.row.id)">批改
                      </el-dropdown-item>
                      <el-dropdown-item @click="$refs.correctTypeSelectorDialog.show(scope.row.id)">重新识别姓名/学号
                      </el-dropdown-item>
                      <el-dropdown-item v-if="scope.row.status === 3" :loading="resultSaving[scope.$index]"
                        @click="saveResult(scope.row.id, scope.$index)" divided>保存结果
                      </el-dropdown-item>
                      <!--                      <el-dropdown-item v-if="scope.row.status !== 2" @click="correctName(scope.row.id)">姓名校正-->
                      <!--                      </el-dropdown-item>-->
                      <el-dropdown-item @click="onEditTask(scope.row)">编辑</el-dropdown-item>
                      <el-dropdown-item @click="doNameCheckAll">姓名校对</el-dropdown-item>
                      <el-dropdown-item @click="doIdCheckAll">学号校对</el-dropdown-item>
                      <el-dropdown-item @click="exportJson(scope.row)">数据导出</el-dropdown-item>
                      <el-dropdown-item v-if="scope.row.status === 2"
                        @click="correctTask(scope.row.id)">重新批改</el-dropdown-item>
                      <el-dropdown-item @click="$refs.bulkCorrectionDialog.show(scope.row.id)">批量纠错
                      </el-dropdown-item>
                      <el-dropdown-item @click="bacthEditStuNo(0)">批量按顺序修改学号
                      </el-dropdown-item>

                      <el-dropdown-item @click="downloadTaskReviewed(scope.row)">下载</el-dropdown-item>
                      <el-dropdown-item @click="onDeleteTask(scope.row.id)" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-space>
            </template>
            <template v-else-if="column.prop === 'record'" v-slot="scope">
              <el-link type="primary" @click="toRecord(scope.row.id)">查看</el-link>
            </template>
            <template v-else-if="column.prop === 'status'" v-slot="scope">
              <el-tag :type="statusOptions[scope.row.status].type">{{
                statusOptions[scope.row.status].label
              }}
              </el-tag>
            </template>
            <template v-else-if="column.prop === 'configName'" v-slot="scope">
              <el-link v-if="scope.row.configId" type="primary" @click="toConfig(scope.row.configId)">
                {{ scope.row.configName }}
              </el-link>
            </template>
            <template v-else-if="column.prop === 'progress'" v-slot="scope">
              <el-text v-if="scope.row.status === 2">{{
                getProgress(scope.row.id)
              }}
              </el-text>
            </template>

          </el-table-column>
        </el-table>
      </div>
      <el-tooltip placement="top" content="拖拽调整左右栏比例">
        <div class="resize-handle" @mousedown="startResize" ref="yinDaoRef1" id="yinDaoRef1"></div>
      </el-tooltip>
      <div class="right-content" v-if="tasks.length">
        <div class="change-task" :style="{ left: (boxWidth + 40) + 'px' }">
          <el-text @click="showChangeTaskMenu">切换正反面</el-text>
          <el-text style="margin-left: 10px" @click="swapConfirm">对页交换</el-text>
          <el-text style="margin-left: 10px" @click="rationConfirm()">旋转180度</el-text>
        </div>
        <cropper v-if="!isResizing" ref="cropper" class="canvas" :id="nowCropper.taskId" :loading="loadingCropper"
          :left-top-text="firstLoadPdf ? '' : (nowCropper.pageIdx + '/' + allTaskData[nowCropper.taskId]?.records?.length + '(' + hasChange + ')')"
          :warning-text="warningText" :success-text="successText" :is-freeze-keyboard="isFreezeKeyboard"
          style="margin-top: 15px" @rect-right-clicked="errorCorrectionFromCropper" @arrow-key-pressed="arrowKeyPressed"
          @rect-right-clicked-input="changeInput">
        </cropper>
        <div v-else class="canvas" style="text-align: center">尺寸调整中</div>
        <div class="pagination">
          <el-row type="flex" align="middle">
            <!-- 文字标签 -->
            <div style="font-weight: bold; color: #409EFF; margin-bottom: 4px">
              正反面：
            </div>
            <el-select v-model="paginationTaskId" size="large" ref="changeTaskId" class="left with-icon"
              placeholder="请选择任务" @change="changeTaskId" style="width: 220px;" prefix-icon="el-icon-notebook-2">
              <el-option v-for="item in tasks" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-row>


          <div style="display: flex;width: 60px;flex-shrink: 0;margin-left: 10px;align-items: center">
            <el-input v-model="pageIdx" size="large" style="width: 45px;flex-shrink: 1" @focus="freezeKeyBoard"
              @blur="unFreezeKeyBoard" @change="changePageIdx">
            </el-input>
            <div style="">页</div>
          </div>
          <div style="width: 5px;flex-shrink: 1"></div>
          <el-pagination background layout="prev, pager, next"
            :total="(!firstLoadPdf && nowCropper.taskId) ? allTaskData[nowCropper.taskId]?.records?.length : 0"
            v-model:current-page="nowCropper.pageIdx" :page-size="1" class="right" @current-change="currentChange">
            <template #prev>
              <span>上一页/键盘左键</span>
            </template>
            <template #next>
              <span>下一页/键盘右键</span>
            </template>
          </el-pagination>

          <el-tooltip class="item" effect="dark" content="勾叉和框的位置整体移动-影响导出和批改 勾叉大小设置（此项不影响导出）" placement="top">
            <span class="icon-with-text">
              <el-icon @click="showXYOffsetDisplacementDialog" color="#007bff" size="20px">
                <Operation />
              </el-icon>
              <span @click="showXYOffsetDisplacementDialog" class="tooltip-text">勾叉/框 位移/大小</span>
            </span>
          </el-tooltip>
        </div>
      </div>
    </div>

    <el-dialog title="请选择打印内容" v-model="printDialogVisible" width="800">
      <el-text style="font-size: 15px;font-weight: bolder;">请先将文件保存到您的电脑</el-text>
      <div style="display: flex;flex-direction: column">
        <el-radio-group v-model="isExportBatch">
          <el-radio :label="false">当前套卷</el-radio>
          <el-radio :label="true">其他套卷</el-radio>
        </el-radio-group>

        <!-- 当前套卷的 PaperSelector -->
        <PaperSelector v-if="!isExportBatch" ref="currentPaperSelector"
          :init-paper="{ fileId: this.fileId, name: this.allTaskData.fileDetail?.name }" :show-folder-name="false"
          :is-current-set="true" />

        <!-- 其他套卷的 PaperSelector -->
        <PaperSelector v-if="isExportBatch" ref="otherPaperSelector"
          :init-paper="{ fileId: this.fileId, name: this.allTaskData.fileDetail?.name, configIds: this.allTaskData.tasks.map(task => task.configId) }"
          :show-folder-name="true" :is-current-set="false" />
      </div>

      <div v-if="hasSwapPaper()">Tip：含原卷会按正确顺序导出，不含原卷按老师扫描的顺序导出</div>
      <el-table :data="localFileTableData" :border="false" style="margin: 10px 0">
        <el-table-column label="" width="60" align="center">
          <template #default="scope">
            <el-checkbox v-model="scope.row.checked"></el-checkbox>
          </template>
        </el-table-column>

        <el-table-column prop="fileName" label="文件名"></el-table-column>
        <el-table-column prop="args" label="参数">
          <template #default="scope">
            <el-text v-if="scope.row.args.enableLevelOutput">已开启等级输出</el-text>
            <el-text v-if="scope.row.args.fontSize">字体大小:{{ scope.row.args.fontSize }}</el-text>
            <el-text v-if="!scope.row.args.isSaveInteger" style="margin-left: 5px">保留一位小数</el-text>
            <el-text v-if="scope.row.args.scorePointTypeShowAverageScore" style="margin-left: 5px">识别分数类型显示平均分</el-text>
            <el-text v-if="scope.row.args.segedScore"> 分段:{{ scope.row.args.segedScore }}分</el-text>
            <el-text v-if="scope.row.args.isReversed"> 交换</el-text>
            <el-text v-if="!scope.row.args.isReversed && scope.row.key !== 'originPaper'"> 不交换</el-text>
            <el-text v-if="scope.row.args.isRotate"> 旋转</el-text>
            <el-text v-if="!scope.row.args.isRotate && scope.row.key !== 'originPaper'"> 不旋转</el-text>
            <el-text v-if="scope.row.args.showQsScore && scope.row.key !== 'originPaper'"> 展示小题分数</el-text>
            <el-text v-if="scope.row.args.onlyShowWrongQsScore && scope.row.key !== 'originPaper'"> 仅展示错误小题分数
            </el-text>
            <el-text v-if="scope.row.key === 'originPaper'"> 用户上传的试卷</el-text>
            <el-text v-for="item in scope.row.args?.needRotationList ?? []">
              <br>面{{ item.orientation }}-第{{ item.pageNumber }}页 旋转180
            </el-text>
            <el-text v-for="item in scope.row.args?.swapPagesList ?? []">
              <br>面{{ item.fromOrientation }}-第{{ item.fromPage }}页 与
              面{{ item.toOrientation }}-第{{ item.toPage }}页 交换
            </el-text>
          </template>
        </el-table-column>

        <el-table-column label="" fixed="right" width="120">
          <template #default="scope">
            <div style="display: flex;align-items: center">
              <el-button link @click="handlePreview(scope.row)" v-if="!isExportBatch">预览</el-button>
              <el-button link @click="previewSettingDialog(scope.row)" icon="Setting">设置</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 对话框底部操作按钮 -->
      <span slot="footer" style="display: flex;justify-content: center">
        <el-button type="primary" @click="handlePrintConfirm(false)"
          style="background: #3981FF;width: 100px;border-radius: 4px;">
          <el-text style="color: #FFFFFF;opacity: 0.9">保存到本地</el-text>
        </el-button>
        <el-button type="primary" @click="$refs.fileSelectorDialog.show()"
          style="background: #3981FF;width: 140px;border-radius: 4px;">
          <el-text style="color: #FFFFFF;opacity: 0.9">保存到远程文件夹</el-text>
        </el-button>
      </span>
    </el-dialog>
    <el-dialog v-model="dialogVisible" title="选择需要保存到远程文件夹的类型" width="500">
      <el-switch v-model="dontNeedSaveFile.switchChoice" active-text="本地不下载"></el-switch>
      <div>
        <el-checkbox-group v-model="remoteFileSaveCheckList">
          <el-checkbox label="统计结果" value="statisticalResults"></el-checkbox>
          <el-checkbox label="批改含原卷" value="correctionIncludeTheOriginalPaper"></el-checkbox>
          <el-checkbox label="批改不含原卷" value="correctionDoesNotIncludeTheOriginalPaper"></el-checkbox>
        </el-checkbox-group>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="save2Remote">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <task-form ref="taskForm" @onClose="taskFormSubmit" />
    <!-- 远程文件列表 -->
    <file-print-selector ref="remoteFilePrintSelectorRef" @openInput="freezeKeyBoard" @closeInput="unFreezeKeyBoard" />
    <canvas ref="uploadCanvas" id="correct-canvas" style="display: none;"></canvas>
    <img src="/flag.svg" id="flagimg" style="display: none;" />
    <img src="/icon/flagWarn.svg" id="flagimgWarn" style="display: none;" />
    <img src="/icon/wrongWarn.svg" id="wrongImgWarn" style="display: none;" />
    <img src="/wrong.svg" id="wrongImg" style="display: none;" />
    <img src="/bandui.svg" id="banduiImg" style="display: none;" />
    <canvas ref="uploadCanvas" id="correct-canvas" style="display: none;"></canvas>
    <img class="cropper-img" :src="imgDataUrl" id="drawImg" style="display: none;">
    <download-form :showTasks="false" ref="downloadForm" @submit="onDownloadDialogSubmit" :submiting="downloading" />
    <stats-download-form :showTasks="false" ref="statsDownloadForm" @submit="onStatsDownloadDialogSubmit"
      :submiting="statsDownloading" />
    <XYOffsetDisplacementDialog :id="nowCropper.taskId" :ids="taskIds" set-mark-zoom ref="XYOffsetDisplacementDialog"
      @submit="handleXYOffsetDisplacementDialog" @refresh="refreshCropper" @close="unFreezeKeyBoard">
    </XYOffsetDisplacementDialog>
    <BulkCorrectionDialog ref="bulkCorrectionDialog" @submit="errorCorrectionBatch"></BulkCorrectionDialog>

    <ChooseExportExcelTypeDialog ref="chooseExportExcelTypeDialog" @submit="chooseExportExcelTypeDialogSubmit">
    </ChooseExportExcelTypeDialog>
    <ScoreTypeRangesDialog ref="scoreTypeRangesDialog" @submit="scoreTypeRangesDialogSubmit"></ScoreTypeRangesDialog>
    <DocCorrectFileSelectDialog ref="docCorrectFileSelectDialog" @submit="docCorrectFileSelectDialogSubmit">
    </DocCorrectFileSelectDialog>
    <FileSelector ref="fileSelectorDialog" type="download" :extra-tip="allTaskData?.fileDetail?.remark"
      @folder-selected="folderSelected" @openInput="freezeKeyBoard" @closeInput="unFreezeKeyBoard"
      :paper-name="allTaskData?.fileDetail?.name"></FileSelector>
    <ClassChangeDialog ref="classChangeDialog" @submit="classChangeAfterRefresh"></ClassChangeDialog>
    <ExcelStyle4Dialog ref="excelStyle4Dialog" @submitStyle4="excelStyle4DialogSubmit" @submitStyle3="exportExcelType3"
      @submitStyle5="exportExcelType5" @submitStyle6="exportExcelType6"></ExcelStyle4Dialog>
    <ExcelStyle1Dialog ref="excelStyle1Dialog" @submit="excelStyle1DialogSubmit"></ExcelStyle1Dialog>
    <CorrectTypeSelectorDialog ref="correctTypeSelectorDialog" @submit="reIdentifyNameOrStudentNumberType">
    </CorrectTypeSelectorDialog>
    <record-form ref="recordForm" @onClose="refreshRecordNameOrStudentNumber" />
    <detail-dialog ref="detailDialog" />
    <el-dialog v-model="editNameDialogVisible" title="编辑名称" width="400px" @close="onEditNameDialogClose">
      <div style="margin-bottom: 8px; font-size: 15px; color: #606266;">请输入新的试卷名称</div>
      <div style="display: flex; align-items: center;">
        <el-input v-model="editNameInput" placeholder="新的试卷名称" style="flex: 1;" />
        <el-tooltip content="复制试卷名称" placement="top">
          <el-button icon="CopyDocument" type="text" @click="copyPaperName(editNameInput)" style="margin-left: 8px;" />
        </el-tooltip>
      </div>
      <template #footer>
        <el-button @click="onEditNameDialogClose">取消</el-button>
        <el-button type="primary" @click="onEditNameDialogConfirm">确定</el-button>
      </template>
    </el-dialog>
    <!-- 区域选择弹窗 -->
    <el-dialog v-model="downloadAreaDialogVisible" title="选择要下载的区域">
      <div style="margin-bottom: 15px;">
        <el-switch 
          v-model="downloadModifiedOnly" 
          active-text="仅下载已修改题目" 
          inactive-text="下载全部题目"
          style="margin-bottom: 10px;">
        </el-switch>
        <div style="font-size: 12px; color: #909399; margin-top: 5px;">
          {{ downloadModifiedOnly ? '只下载包含已修改题目的区域图片' : '下载选中区域的所有图片' }}
        </div>
      </div>
      <el-checkbox-group v-model="selectedAreas">
        <el-checkbox v-for="area in currentDownloadAreas" :key="area.areaIdx" :label="area.areaIdx">{{ area.name
        }}</el-checkbox>
      </el-checkbox-group>
      <template #footer>
        <el-button @click="downloadAreaDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleBatchDownload">下载</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { useUserStore } from "@/store/index";
import { mapActions, mapState } from "pinia";
import TaskForm from '@/views/doccorrect/components/taskform.vue';
import Cropper from '@/components/cropper/forPreview.vue';
import Record from "@/views/markPapers/components/record.vue";
import * as pdfjsLib from 'pdfjs-dist';
import FilePrintSelector from "@/views/common/FilePrintSelector.vue";
import { More } from "@element-plus/icons-vue";
import StatsDownloadForm from "@/views/doccorrect/components/statsdownload-form.vue";
import DownloadForm from "@/views/doccorrect/components/download-form.vue";
import XYOffsetDisplacementDialog from '@/views/common/XYOffsetDisplacementDialog.vue'
import BulkCorrectionDialog from "@/views/markPapers/components/BulkCorrectionDialog.vue";
import { createTasksExcelData, createTasksExcelDataType2, getScoreTypeAndMaxScore } from "@/utils/CorrectDataHandler";
import ChooseExportExcelTypeDialog from '@/views/markPapers/components/chooseExportExcelTypeDialog.vue'
import ScoreTypeRangesDialog from '@/views/markPapers/components/scoreTypeRangesDialog.vue'
import DocCorrectFileSelectDialog from './docCorrectFileSelectDialog.vue'
import * as XLSX from 'xlsx';
import FileSelector from "@/views/common/FileSelector.vue";
import { initScoreRange } from "@/utils/scoreRangeItem";
import ClassChangeDialog from './classChangeDialog.vue'
import ExcelStyle4Dialog from './excelStyle4Dialog.vue'
import ExcelStyle1Dialog from './excelStyle1Dialog.vue'
import PaperSelector from './PaperSelector.vue';
import { useTransition } from "@vueuse/core";
import { computed, ref } from "vue";
import CorrectTypeSelectorDialog from './CorrectTypeSelectorDialog.vue'
import confetti from "canvas-confetti";
import RecordForm from "@/views/doccorrect/components/recordForm.vue";
import DetailDialog from "@/views/markPapers/components/DetailDialog.vue";
import JSZip from 'jszip';

const store = useUserStore();

function handleClick() {
  const end = Date.now() + 3 * 1000; // 3 seconds
  const colors = ["#a786ff", "#fd8bbc", "#eca184", "#f8deb1"];

  // Frame function to trigger confetti cannons
  function frame() {
    if (Date.now() > end) return;

    // Left side confetti cannon
    confetti({
      particleCount: 2,
      angle: 60,
      spread: 55,
      startVelocity: 60,
      origin: { x: 0, y: 0.5 },
      colors: colors,
    });

    // Right side confetti cannon
    confetti({
      particleCount: 2,
      angle: 120,
      spread: 55,
      startVelocity: 60,
      origin: { x: 1, y: 0.5 },
      colors: colors,
    });

    requestAnimationFrame(frame); // Keep calling the frame function
  }

  frame();
}

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export default {
  components: {
    DetailDialog,
    RecordForm,
    FileSelector,
    DownloadForm,
    StatsDownloadForm,
    More,
    FilePrintSelector,
    Record,
    Cropper,
    TaskForm,
    XYOffsetDisplacementDialog,
    BulkCorrectionDialog,
    ChooseExportExcelTypeDialog,
    ScoreTypeRangesDialog,
    DocCorrectFileSelectDialog,
    ClassChangeDialog,
    ExcelStyle4Dialog,
    ExcelStyle1Dialog,
    PaperSelector,
    CorrectTypeSelectorDialog
  },
  data() {
    return {
      downloadNotify: null,
      downloading: false,
      downloadProgress: 0,
      downloadTotal: 0,
      downloadStatus: '',
      fileId: this.$route.params.fileId,
      filter: {
        name: "",
        status: null,
      },
      tasks: [],
      columns: [
        { label: "", prop: "name", width: 60 },
        { label: "试卷配置", prop: "configName" },
        // {label: "试卷", prop: "record", width: 80},
        { label: "状态", prop: "status", width: 100 },
        { label: "进度", prop: "progress", width: 80 },
        { label: "操作", prop: "operations", width: 70 },
      ],
      taskLoading: false,
      statusOptions: {
        1: { value: 1, label: "待批改", type: "primary" },
        2: { value: 2, label: "批改中", type: "warning" },
        3: { value: 3, label: "批改完成", type: "success" },
      },
      refresher: null,
      statusStats: {
        1: 0,
        2: 0,
        3: 0,
        total: 0,
      },
      downloading: false,
      statsDownloading: false,
      resultSaving: {},
      timer: null,
      file: null,
      expandRows: [],
      cropperDetail: {},
      nowCropper: {
        // 当前选中的task的id，第几个paper和第几页（+1的关系）,当前docUrl
        taskId: null,
        // 真实的数组idx
        paperIdx: null,
        pageIdx: null,
        docUrl: null
      },
      paginationTaskId: null,
      changePaginationLoading: false,
      autoCorrect: false,
      loadingCropper: false,
      isRefreshingDataAndDontNeedInitCropper: false,
      firstLoadNeedInitCropper: true,
      firstLoadPdf: true,
      imgDataUrl: null,
      dialogVisible: false,
      remoteFileSaveCheckList: [],
      // 本地是否需要下载
      dontNeedSaveFile: {
        statisticalResults: false,
        correctionIncludeTheOriginalPaper: false,
        correctionDoesNotIncludeTheOriginalPaper: false,
        originPaper: false,
        switchChoice: true
      },
      printDialogVisible: false,
      checkedList: [],
      isExportBatch: false,
      localFileDetails: [],
      exportBatchFileDetails: [],
      exportBatchPapers: [],
      localFileTableData: [
        {
          key: 'stats',
          fileName: '统计结果',
          checked: true,
          args: {
            fontSize: 20,
            tasks: [],
            save2Remote: false,
            printerProps: { print: false },
            segments: [],
            isSaveInteger: true,
            scorePointTypeShowAverageScore: false,
          }
        },
        {
          key: 'reviewWithOrig',
          fileName: '批改含原卷',
          checked: true,
          args: {
            showQsScore: false,
            onlyShowWrongQsScore: false,
            scoreMerge: true,
            isPreview: true,
            enableLevelOutput: false,
            needRotationList: [],
            swapPagesList: []
          }
        },
        {
          key: 'reviewWithoutOrig',
          fileName: '批改不含原卷',
          checked: true,
          args: {
            isPreview: false,
            isReversed: true,
            isRotate: true,
            scoreMerge: true,
            showQsScore: false,
            onlyShowWrongQsScore: false,
            enableLevelOutput: false,
            needRotationList: [],
            swapPagesList: []
          }
        },
        {
          key: 'originPaper',
          fileName: '原试卷',
          checked: false,
          args: {
            isPreview: false,
            isReversed: false,
            isRotate: false,
            scoreMerge: false,
            showQsScore: false,
            onlyShowWrongQsScore: false,
            enableLevelOutput: false
          }
        }
      ],
      imagesList: {},
      loadingImagesList: [],
      base64Images: null,
      allTaskData: {},
      XYOffsetData: {
        xOffset: 0,
        yOffset: 0
      },
      pageIdx: 1,
      boxWidth: 580,
      isResizing: false,
      startX: 0,
      warningText: '',
      successText: '',
      useAimodel: '',
      refreshSetTimeout: null,
      folderPath: '',
      isFreezeKeyboard: false,
      students: null,
      isFreezeKeyboardTempSave: false,
      boxWidthInterval: null,
      needMarkPaperFileTaskGuide: false,
      rpm: 0,
      forceAdjust: false,
      hasChange: '未修改',
      batchWaitingDownLoadFileList: {
        stats: [],
        reviewWithOrig: [],
        reviewWithoutOrig: [],
        originPaper: []
      },
      debounceTimers: {},
      haveLoadedDocType: false,
      isExportBatching: false,
      save2RemoteSuccessCnt: 0,
      haveLoadSinglePdf: false,
      convertIdx: 0,
      paperSize: 0,
      pdfData: null,
      stopConvertSinglePdfUrlToBase64: false,
      editNameDialogVisible: false,
      editNameInput: '',
      editNameId: null,
      downloadAreaDialogVisible: false,
      selectedAreas: [],
      currentDownloadTask: null,
      currentDownloadAreas: [],
      downloadModifiedOnly: false,
    }
  },
  beforeDestroy() {
    this.stopConvertSinglePdfUrlToBase64 = true;
  },
  created() {
    // let allWidth = window.innerWidth - 216;
    // let rightWidth = (allWidth - 22) / 3 * 2;
    // this.boxWidth = allWidth - 22 - rightWidth;
    this.boxWidth = store.getMarkPaperLeftWidth;
    this.needMarkPaperFileTaskGuide = store.needMarkPaperFileTaskGuide;
    this.fileId = this.$route.params.fileId;
  },
  watch: {
    "nowCropper.pageIdx"(val) {
      this.nowCropper.paperIdx = val - 1;
      const record = this.allTaskData?.[this.nowCropper.taskId]?.records?.[val - 1] ?? null;
      this.nowCropper.docUrl = record?.docurl ?? null;
      if (!this.firstLoadPdf) {
        this.setNowCropperByFileIds(this.fileId, this.nowCropper);
      }
      this.forceAdjust = true;
      this.hasChange = record?.hasChange ? '已修改' : '未修改';
      this.initCropper();
      // 判断是否是最后一页
      if (val === this.allTaskData?.[this.nowCropper.taskId]?.records?.length && val !== null) {
        if (this.nowCropper.taskId === this.allTaskData.tasks[this.allTaskData.tasks.length - 1].id) {
          // 最后一页
          this.finishCropper();
        }
      }
      // 弹窗提示
      let successText = `已切换到 ${this.nowCropper.pageIdx}/${this.allTaskData?.[this.nowCropper.taskId]?.records?.length ?? '无'} 页`;
      if (this.$refs.cropper?.isFullScreen) {
        this.successText = successText;
        setTimeout(() => {
          this.successText = ''
        }, 2000)
      } else {
        this.$message({
          message: successText,
          type: 'success',
          duration: 2000
        });
      }

    },
    "nowCropper.taskId"(val) {
      const record = this.allTaskData?.[val]?.records?.[this.nowCropper.paperIdx] ?? null;
      this.nowCropper.docUrl = record?.docurl ?? null;
      if (this.nowCropper.docUrl) {
        if (!this.firstLoadPdf) {
          this.setNowCropperByFileIds(this.fileId, this.nowCropper);
        }
        this.forceAdjust = true;
        this.initCropper();
      }
    }
  },
  beforeUnmount() {
    if (this.timer) {
      clearInterval(this.timer);
    }
    this.timer = null;
  },
  computed: {
    ...mapState(useUserStore, ["getUser", "getAimodelOptions"]),
    corrected() {
      let res = false;
      if (this.tasks.length > 0) {
        res = !this.tasks.find(item => item.status !== 3);
      }
      if (res) {
        this.$emit('setActiveStep', 3);
      } else {
        this.$emit('setActiveStep', 2);
      }
      return res;
    },
    taskIds() {
      let ids = [];
      let tasks = this.allTaskData?.tasks ?? [];
      tasks.forEach((task) => {
        ids.push(task.id);
      })
      return ids;
    },
    modelLabel() {
      return this.allTaskData?.modelDetail?.name;
    },
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.fileId = vm.$route.params.fileId;
      vm.loadFile()
    })
  },
  methods: {
    ...mapActions(useUserStore, ['setXYOffsetData', 'setNeedMarkPaperFileTaskGuide', 'setNowCropperByFileIds']),
    showXYOffsetDisplacementDialog() {
      this.freezeKeyBoard();
      this.$refs.XYOffsetDisplacementDialog.show();
    },
    addErrorCorrection() {
      if (!this.allTaskData || !this.allTaskData.fileDetail) {
        return;
      }
      const param = {
        fileId: this.allTaskData.fileDetail.id,
        taskId: this.nowCropper.taskId,
        recordId: this.allTaskData[this.nowCropper.taskId].records[this.nowCropper.paperIdx].id
      }
      this.$axios.post('/api/errorCorrectionTime/add', param).then((res) => {

      })
    },
    swapConfirm(idx, recordId) {
      let messageText = ' 含原卷会按正确顺序导出，不含原卷按老师扫描的顺序导出 【不需要通知老师交换试卷位置】';
      this.$confirm('是否确认<span style="color: #E6A23C;">对页交换</span>，该功能仅在【扫描错误】时使用。<br/>（默认双面的就是互相交换，4页的就是1-3/2-4互换)<br/>导出时：【不含原卷】的批改结果会交换、【含原卷】的扫描页面会交换；【含原卷】 的批改结果不会交换。<br/><span style="color: #E6A23C;">不需要</span>提醒老师修改试卷顺序。', '', {
        confirmButtonText: '交换且重批这两页',
        cancelButtonText: '仅交换',
        type: 'warning',
        distinguishCancelAndClose: true,
        dangerouslyUseHTMLString: true,
        width: '800px'
      }).then(res => {
        this.swap(this.nowCropper.paperIdx, this.allTaskData[this.nowCropper.taskId].records[this.nowCropper.paperIdx].id, this.nowCropper.taskId, true)
        this.$message.warning({
          message: messageText,
          duration: 10000,
        });
      }).catch((type) => {
        if (type === 'cancel') {
          this.$message.warning({
            message: messageText,
            duration: 10000,
          });
          this.swap(this.nowCropper.paperIdx, this.allTaskData[this.nowCropper.taskId].records[this.nowCropper.paperIdx].id, this.nowCropper.taskId, false)
        }
      })
    },
    rationConfirm(recordId = null) {
      if (!recordId) {
        recordId = this.allTaskData[this.nowCropper.taskId].records[this.nowCropper.paperIdx].id;
      }
      this.$confirm('是否确认<span style="color: #E6A23C;">旋转 180 度</span>，该功能仅在【扫描错误】时使用。（仅旋转当前页面）<br/>导出时：【不含原卷】的批改结果、【含原卷】的扫描页面会旋转 180 度；【含原卷】的批改结果不会旋转；<br/><span style="color: #E6A23C;">不需要</span>提醒老师修改试卷顺序。', '', {
        type: 'warning',
        distinguishCancelAndClose: true,
        dangerouslyUseHTMLString: true,
        width: '800px'
      }).then(res => {
        if (res === 'confirm') {
          this.$axios.get(`/api/docCorrectRecord/ration180?id=${recordId}`).then(() => {
            // 旋转成功后先弹窗提示，再延迟刷新页面
            let messageText = '含原卷、不含原卷按照旋转后的格式导出【需要提醒老师旋转试卷】';
            this.$message.warning({
              message: messageText,
              duration: 1000,
            });
            setTimeout(() => {
              window.location.reload();
            }, 500);
          }).catch((error) => {
            this.$message.error('旋转失败，请稍后重试');
          });
        }
      });
    },
    classChangeAfterRefresh() {
      this.$confirm("是否立即重新识别姓名/学号", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(res => {
        this.$refs.correctTypeSelectorDialog.show(this.tasks[0].id)
      })
      this.refresh();
    },
    editRemark(id, remark) {
      this.freezeKeyBoard();
      this.$prompt('纠错完成请备注：XXX完', '修改备注', {
        inputValue: remark ?? '',
        confirmButtonText: '修改',
        cancelButtonText: '取消',
      }).then(({ value }) => {
        const param = {
          id: id,
          remark: value
        }
        this.$axios.post("/api/docCorrectFile/update", param).then(res => {
          // 更新
          this.$message.success("修改成功！")
          this.allTaskData.fileDetail.remark = value;
          this.refresh();
        })
      }).catch((e) => {
        // 用户点击取消时的处理逻辑
        this.$message.info("已取消修改");
      }).finally(() => {
        this.unFreezeKeyBoard()
      })
    },
    copyRemark(text) {
      if (!text) {
        this.$message.warning('暂无备注可复制');
        return;
      }
      // 使用浏览器原生剪贴板 API
      navigator.clipboard.writeText(text)
        .then(() => {
          this.$message.success('备注已复制到剪贴板');
        })
        .catch(() => {
          this.$message.error('复制失败，请手动复制');
        });
    },
    closeConvertpdf2Img() {
      this.$confirm("是否确认关闭缓存图片?不影响其他操作和界面预览，缓存只会加快后面的图片预览", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(res => {
        this.stopConvertSinglePdfUrlToBase64 = true;
      })
    },
    showChangeTaskMenu() {
      this.$refs.changeTaskId.toggleMenu()
      // this.$message.info('已弹出，请选择')
    },
    unFreezeKeyBoard() {
      this.isFreezeKeyboard = this.isFreezeKeyboardTempSave;
    },
    freezeKeyBoard() {
      this.isFreezeKeyboardTempSave = this.isFreezeKeyboard;
      this.isFreezeKeyboard = true;
    },
    openPrintDialog() {
      if (!this.haveLoadedDocType) {
        const isImageWidthBigger = this.$refs.cropper.isImageWidthBigger;
        // this.localFileTableData[2].args.isReversed = isImageWidthBigger;
        this.localFileTableData[2].args.isRotate = isImageWidthBigger;
        this.haveLoadedDocType = true;
      }
      this.printDialogVisible = true;
    },
    swap(idx, recordId, taskId, execute) {
      let taskSize = this.allTaskData.tasks.length;
      if (taskSize !== 2 && taskSize !== 4) {
        this.$message.error("仅支持2页/4页的交换");
      }
      let targetRecordId = '';
      let targetTaskIdx = -1;
      let taskIdx = this.allTaskData.tasks.findIndex((item) => item.id === taskId);
      if (taskSize === 2) {
        targetTaskIdx = taskIdx == 0 ? 1 : 0;
      } else {
        switch (taskIdx) {
          case 0:
            targetTaskIdx = 2;
            break;
          case 1:
            targetTaskIdx = 3;
            break;
          case 2:
            targetTaskIdx = 0;
            break;
          case 3:
            targetTaskIdx = 1;
            break;
        }
      }
      let targetTaskId = this.allTaskData.tasks[targetTaskIdx].id;
      targetRecordId = this.allTaskData[targetTaskId].records[idx].id;
      this.$axios.get(`/api/docCorrectFile/swap?recordId=${recordId}&targetRecordId=${targetRecordId}`).then(res => {
        // 更新
        this.$message.success("对调成功")
        if (execute) {
          let form1 = {
            id: targetTaskId,
            aimodel: this.useAimodel || store.getDefaultCorrectModal,
            ocrType: '2',
            responseFormat: true
          }
          if (recordId) {
            form1.recordIds = [targetRecordId]
          }
          this.$axios.post("/api/docCorrectTask/execute", form1).then(res => {
            this.$message.success("提交成功")
            this.refresh()
          })
          let form2 = {
            id: taskId,
            aimodel: this.useAimodel || store.getDefaultCorrectModal,
            ocrType: '2',
            responseFormat: true
          }
          if (recordId) {
            form2.recordIds = [recordId]
          }
          this.$axios.post("/api/docCorrectTask/execute", form2).then(res => {
            this.$message.success("提交成功")
            this.refresh()
          })
        } else {
          this.refresh();
        }

        let pageIdx = this.nowCropper.pageIdx;
        if (this.nowCropper.pageIdx === 1) {
          this.nowCropper.pageIdx = 2;
        } else {
          this.nowCropper.pageIdx = 1;
        }
        setTimeout(() => {
          this.nowCropper.pageIdx = pageIdx;
        }, 2000)
      })


    },
    finishCropper(showMessageAndRemark = false) {
      this.addErrorCorrection()
      const param = {
        id: this.fileId,
        isCorrectFinish: 1
      }
      this.$axios.post("/api/docCorrectFile/update", param).then(res => {
        // 更新
        this.$message.success("恭喜您完成到最后一页！")
        if (showMessageAndRemark) {
          handleClick();
          this.editRemark(this.allTaskData?.fileDetail.id, this.allTaskData?.fileDetail.remark)
        }

      })
    },
    getProgress(taskId) {
      const totalCount = this.allTaskData[taskId].records.length;
      const completedCount = this.allTaskData[taskId].records.filter(item => item.status === 4).length;
      return `${completedCount}/${totalCount}`;
    },
    finishTour() {
      this.needMarkPaperFileTaskGuide = false;
      this.setNeedMarkPaperFileTaskGuide(false);
    },
    taskFormSubmit() {
      this.isFreezeKeyboard = this.isFreezeKeyboardTempSave;
      this.refresh();
    },
    async fetchDetailAndShow() {
      const loadingInstance = this.$message.warning({
        message: '正在加载详细统计信息，请稍候...',
        duration: 0, // 设置为 0 以保持加载状态
      });
      // —— 1. 计算各任务耗时 timesStr ——
      const resp = await this.$axios.get(
        `/api/errorCorrectionTime/statisticsByFileId?fileId=${this.fileId}`
      );
      let timesStr = '';
      if (resp.code === 200 && resp.data.length) {
        const map = resp.data[0].times;
        const names = this.allTaskData.tasks.map(t => ({ id: t.id, name: t.name }));
        for (const [id, sec] of Object.entries(map)) {
          const name = (names.find(n => String(n.id) === id) || { name: `任务${id}` }).name;
          const m = Math.floor(sec / 60), s = sec % 60;
          timesStr += `${name}：${m}分${s}秒； `;
        }
      }

      // —— 2. 计算总批改时长 ——
      let durationText = '';
      if (this.file.lastCorrectTime && this.file.finishCorrectTime) {
        const diff = new Date(this.file.finishCorrectTime) - new Date(this.file.lastCorrectTime);
        const m = Math.floor(diff / 60000), s = Math.floor((diff % 60000) / 1000);
        durationText = `${m}分${s}秒`;
      }

      // —— 3. 统计修改量与总量 ——
      let modified = 0, totalCount = 0, failCount = 0, failIsModifyCount = 0;
      // 计算试卷总分、学生数量、平均分、中位数
      let paperScoreObjs = [];
      this.allTaskData.tasks.forEach(task => {
        this.allTaskData[task.id].records.forEach((rec, recIndex) => {
          rec.reviewedObj.forEach(area => {
            let isFail = area.status === 2
            area.reviewed.forEach(q => {
              totalCount++;
              if (isFail) failCount++;
              if (q.hasChange === 1) modified++;
              if (q.hasChange === 1 || isFail) failIsModifyCount++;
            });
          });
        });
      });
      this.allTaskData[this.nowCropper.taskId].records.forEach((rec, recIndex) => {
        const scoreObj = this.getPaperScore(recIndex);
        paperScoreObjs.push(scoreObj);
      });

      const accuracy1 = (100 - ((modified) / totalCount) * 100).toFixed(2);
      const accuracy2 = (100 - (failIsModifyCount / totalCount) * 100).toFixed(2);
      const failCountPercentage = ((failCount / totalCount) * 100).toFixed(2);

      let stats = {};
      if (!this.allTaskData.fileDetail.averageTimeConsumption) {
        const statsResponse = await this.$axios.get(
          `/api/gptAskLog/statsByFileId?fileId=${this.fileId}`
        );
        stats = statsResponse.data || {};
        this.refresh();
      } else {
        stats = {
          "averageTimeConsumption": this.allTaskData.fileDetail.averageTimeConsumption,
          "totalTimeConsumption": this.allTaskData.fileDetail.totalTimeConsumption,
          "totalCount": this.allTaskData.fileDetail.totalCount,
          "nullCount": this.allTaskData.fileDetail.nullCount,
        }
      }


      const fullScores = paperScoreObjs.map(obj => obj.scored);
      const totalScoreSum = fullScores.reduce((acc, s) => acc + s, 0);
      const studentCount = fullScores.length;
      const averageScore = studentCount > 0
        ? (totalScoreSum / studentCount).toFixed(2)
        : "0.00";
      let medianScore = "0.00";
      if (studentCount > 0) {
        // 对实得分进行升序排序
        const sorted = [...fullScores].sort((a, b) => a - b);
        const mid = Math.floor(studentCount / 2);
        if (studentCount % 2 === 1) {
          // 样本量为奇数：取中位数值
          medianScore = sorted[mid].toFixed(2);
        } else {
          // 样本量为偶数：取中间两个数的平均值
          medianScore = ((sorted[mid - 1] + sorted[mid]) / 2).toFixed(2);
        }
      }
      // —— 4. 填充并显示对话框 ——
      this.detailData = {
        modelLabel: this.modelLabel,
        modifiedCount: failIsModifyCount,
        totalCount,
        failCount,
        failCountPercentage,
        accuracy1,
        accuracy2,
        timesStr,
        durationText,
        stats: stats,
        totalScoreSum,
        studentCount,
        averageScore,
        medianScore,
        onePaperTotalScore: paperScoreObjs[0]?.total || 0,
      };
      loadingInstance.close();
      this.$refs.detailDialog.show(this.detailData);
    },
    /**
     * 点击"编辑名称"按钮时调用，弹出输入框修改试卷名称，且在提交前做名称重复检查
     * @param {number} id - 当前行的试卷 ID
     * @param {string} currentName - 当前试卷的名称，用于在输入框中显示初始值
     */
    onEditName(id, currentName) {
      this.editNameId = id;
      this.editNameInput = currentName;
      this.editNameDialogVisible = true;
      this.freezeKeyBoard();
    },
    onEditNameDialogClose() {
      this.editNameDialogVisible = false;
      this.unFreezeKeyBoard();
    },
    onEditNameDialogConfirm() {
      const newName = this.editNameInput.trim();
      if (!newName) {
        this.$message.error('名称不能为空');
        return;
      }
      // 检查名称是否重复
      this.$axios.get(`/api/docCorrectFile/checkName?name=${encodeURIComponent(newName)}`)
        .then((res) => {
          if (res.data?.exists) {
            this.$message.error('试卷名称已存在，请使用其他名称');
            return;
          }
          // 名称不重复，继续调用更新接口
          const payload = { id: this.editNameId, name: newName };
          this.$axios.post('/api/docCorrectFile/update', payload)
            .then(() => {
              this.$message.success('修改成功！');
              this.editNameDialogVisible = false;
              this.refresh();
            })
            .catch((err) => {
              console.error(err);
              this.$message.error('修改失败，请稍后重试');
            })
            .finally(() => {
              this.unFreezeKeyBoard();
            });
        })
        .catch((err) => {
          console.error(err);
          this.$message.error('检查名称时发生错误，请稍后重试');
        });
    },
    copyPaperName(name) {
      if (!name) return;
      navigator.clipboard.writeText(name)
        .then(() => {
          this.$message.success('已复制到剪贴板');
        })
        .catch(() => {
          this.$message.error('复制失败');
        });
    },
    openClassChangeDialog() {
      this.$refs.classChangeDialog.show({
        classId: this.allTaskData.fileDetail?.classId,

        id: this.allTaskData.fileDetail.id
      }, this.allTaskData?.class?.schoolId, this.allTaskData?.class?.className);
    },
    loadStudents() {
      if (!this.students && this.allTaskData.fileDetail && this.allTaskData.fileDetail?.classId) {
        this.$axios.get('/api/user/student/list/' + this.allTaskData.fileDetail.classId).then(res => {
          this.students = res.data;
        })
      }
    },
    calcScore() {
      if (!this.nowCropper.taskId || !this.allTaskData) {
        return ''
      }

      let { scored, total, addScored, addTotal, addName } = this.getPaperScore(this.nowCropper.paperIdx);
      if (addTotal > 0) {
        return `总分:${scored}/${total}  ${addName}: ${addScored}/${addTotal}`
      } else {
        return `总分:${scored}/${total}`
      }
    },
    getPaperScore(paperIdx) {
      let configs = this.allTaskData.tasks.map((item) => {
        return this.allTaskData[item.id].config;
      });
      let records = this.allTaskData.tasks.map((item) => {
        return this.allTaskData[item.id].records[paperIdx];
      });
      let scored = 0
      let total = 0
      let addScored = 0
      let addTotal = 0
      let addName = configs?.[0].configObj.additionalName || '附加';

      configs.forEach((config, configIdx) => {
        let record = records[configIdx];
        if (!record) return;
        config.areasObj.forEach((area, areaIdx) => {
          // 手写识别
          let areaType = area.areaType
          if (area.enabled) {
            area.questions.forEach((qs, qsIdx) => {
              let isAdd = (qs.isAdditional || 1) !== 1;
              if (qs.isScorePoint === 2 || areaType === 4) {
                // 按照得分点给分
                scored += parseInt(record?.reviewedObj[areaIdx]?.reviewed?.[qsIdx]?.scored || 0)
              } else {
                let isTrue = (record?.reviewedObj[areaIdx]?.reviewed?.[qsIdx]?.isCorrect || 'Y') === 'Y'
                if (record?.reviewedObj[areaIdx]?.reviewed?.[qsIdx]?.isEssay) {
                  scored += isAdd ? 0 : parseFloat(record?.reviewedObj[areaIdx]?.reviewed?.[qsIdx]?.scored)
                } else if (isTrue) {
                  scored += isAdd ? 0 : qs.score
                  addScored += isAdd ? qs.score : 0
                }
              }
              total += isAdd ? 0 : qs.score
              addTotal += isAdd ? qs.score : 0
            })
          }
        })
      })
      return {
        scored: scored,
        total: total,
        addScored: addScored,
        addTotal: addTotal,
        addName: addName
      }
    },
    refreshRecordNameOrStudentNumber(e, sameNumberCount) {
      this.isFreezeKeyboard = false;
      this.allTaskData[this.nowCropper.taskId].records[this.nowCropper.paperIdx].identify = e.identify;
      this.allTaskData[this.nowCropper.taskId].records[this.nowCropper.paperIdx].studentNumber = e.studentNumber;
      this.preview();
      if (sameNumberCount && this.$refs.cropper.isFullScreen) {
        // 提醒
        this.warningText = `当前试卷有${sameNumberCount}个重复的学号${e.studentNumber}，请注意！`
        setTimeout(() => {
          this.warningText = '';
        }, 3000)
      }
    },
    showEditNameOrStudentNumber() {
      this.$refs.recordForm.show(this.allTaskData[this.nowCropper.taskId].records[this.nowCropper.paperIdx], this.students)
      this.freezeKeyBoard();
    },
    changeInput(value, type, clickIndexScorePoint) {
      if (!value && (type === 'studentNumber' || type === 'identify')) {
        // 非全屏进行编辑
        this.showEditNameOrStudentNumber();
      } else if (value && (type === 'studentNumber' || type === 'identify')) {
        // 直接修改
        const record = this.allTaskData[this.nowCropper.taskId].records[this.nowCropper.paperIdx];
        const param = {
          id: record.id,
          identify: type === 'identify' ? value : record.identify,
          studentNumber: type === 'studentNumber' ? value : record.studentNumber,
        }
        this.$axios.post("/api/docCorrectRecord/update", param).then(res => {
          // 更新
          this.successText = `修改${type === 'identify' ? '姓名' : '学号'}成功！`;
          setTimeout(() => { this.successText = '' }, 3000);

          this.$refs[this.nowCropper.taskId][this.$refs[this.nowCropper.taskId].length - 1].refreshRecordNameOrStudentNumber(param)
        })
      } else if (type === 'scorePoint') {
        // 纠错
        this.errorCorrectionFromCropper(clickIndexScorePoint, value)
      }

    },
    folderSelected(path) {
      this.folderPath = path;
      this.handlePrintConfirm(true);
    },
    excelStyle1DialogSubmit(e) {
      this.exportExcel(e);
    },
    excelStyle4DialogSubmit(e) {
      this.exportExcelType4(e)
    },
    docCorrectFileSelectDialogSubmit(e) {
      this.exportExcelType3(e)
    },
    getRanges() {
      if ('ranges' in this.allTaskData[this.tasks[0].id].config.configObj) {
        let ranges = this.allTaskData[this.tasks[0].id].config.configObj.ranges
        if (!ranges || !Object.keys(ranges).length) {
          //如果导出表格时不包含ranges数据，则默认导出不含ranges数据的表格，不会弹出报错信息
          //this.$message.error('请先设置分数段');
          return null;
        } else {
          return ranges;
        }
      } else {
        //this.$message.error('请先设置分数段');
        return null;
      }
    },
    initScoreRanges() {
      let needInit = false;
      let ranges = {};
      let enableLevelOutput = this.allTaskData[this.tasks[0].id].config.configObj.enableLevelOutput;
      if (!enableLevelOutput) {
        return this.setPreviewRanges({}, enableLevelOutput)
      }
      if ('ranges' in this.allTaskData[this.tasks[0].id].config.configObj) {
        let ranges = this.allTaskData[this.tasks[0].id].config.configObj.ranges
        if (!ranges || !Object.keys(ranges).length) {
          needInit = true;
        } else {
          this.setPreviewRanges(ranges, enableLevelOutput)
          return ranges;
        }
      } else {
        needInit = true;
      }

      if (needInit) {
        this.$axios.get(`/api/docCorrectConfigPackage/getAllScoreTypeScore?id=${this.allTaskData[this.tasks[0].id].config.id}`).then(res => {
          ranges = initScoreRange(res.data.scoreTypes, res.data.scoreTypesMaxScore);
          this.allTaskData[this.tasks[0].id].config.configObj.ranges = ranges;
          this.setPreviewRanges(ranges, enableLevelOutput);
        })
      } else {
        this.setPreviewRanges(ranges, enableLevelOutput);
      }
    },
    setPreviewRanges(ranges, enableLevelOutput) {
      this.localFileTableData[1].args.ranges = ranges;
      this.localFileTableData[2].args.ranges = ranges;
      this.localFileTableData[1].args.enableLevelOutput = enableLevelOutput;
      this.localFileTableData[2].args.enableLevelOutput = enableLevelOutput;
    },
    scoreTypeRangesDialogSubmit(e) {
      let ranges = this.getRanges();
      // 不再判断 ranges 是否为 null，直接赋值并导出
      e.ranges = ranges;
      this.exportExcelType2(e);
    },
    chooseExportExcelTypeDialogSubmit(e) {
      if (e === 'style1') {
        this.$refs.excelStyle1Dialog.show()
      } else if (e === 'style2') {
        this.$refs.scoreTypeRangesDialog.show(
          this.allTaskData?.school?.schoolId,
          this.allTaskData?.class?.classId
        );
      } else if (e === 'style3') {
        const tasks = this.tasks.map(task => this.allTaskData[task.id])
        let data = getScoreTypeAndMaxScore(tasks);
        let configName = this.allTaskData[this.allTaskData.tasks[0].id].config.name.split('_').slice(0, -1).join('_');
        let configIds = this.allTaskData.tasks.map(task => task.configId)
        this.$refs.docCorrectFileSelectDialog.show(data, this.fileId, this.getRanges(), configIds)
      } else if (e === 'style4' || e === 'style5') {
        const tasks = this.tasks.map(task => this.allTaskData[task.id])
        let data = getScoreTypeAndMaxScore(tasks);
        let configIds = this.allTaskData.tasks.map(task => task.configId)
        // 获取班级学生数量
        const realClassNum = this.students ? this.students.length : 0;
        this.$refs.excelStyle4Dialog.show(data, this.fileId, this.getRanges(), configIds, e === 'style5', realClassNum)
      }
    },
    startResize(event) {
      this.isResizing = true;
      this.startX = event.clientX;
      document.addEventListener("mousemove", this.onMouseMove);
      document.addEventListener("mouseup", this.onMouseUp);
    },
    onMouseMove(event) {
      if (this.isResizing) {
        const deltaX = event.clientX - this.startX;
        this.boxWidth += deltaX;
        this.startX = event.clientX;
        // 延时更改
        if (this.boxWidthInterval === null) {
          clearTimeout(this.boxWidthInterval);
        }
        this.boxWidthInterval = setTimeout(() => {
          store.setMarkPaperLeftWidth(this.boxWidth);
        }, 1500);

      }
    },
    onMouseUp() {
      this.isResizing = false;
      document.removeEventListener("mousemove", this.onMouseMove);
      document.removeEventListener("mouseup", this.onMouseUp);
      this.$nextTick(() => {
        this.initCropper();
      })
      // this.initCropper()
    },
    changePageIdx(e) {
      let nowCropper = JSON.parse(JSON.stringify(this.nowCropper));
      if (e === '0') {
        return this.$message.error('请输入正确的页码');
      } else if (e > this.allTaskData[nowCropper.taskId].records.length) {
        return this.$message.error('页码超出范围');
      }
      nowCropper.pageIdx = parseInt(e);
      this.nowCropper = nowCropper;
    },
    errorCorrectionBatch(e) {
      this.$axios.get('/api/docCorrectRecord/qs/correctbyBatch?taskId=' + e.taskId + '&isUpdateHasChanged=' + e.correctModified + '&qsIdx=' + (e.questionIndex - 1) + '&isRight=' + e.setAllCorrect).then(res => {
        this.$message.success('批量纠错成功' + res.data + '个');
        if (res.data) {
          window.location.reload();
        }
      })
      this.initCropper();
    },
    handleXYOffsetDisplacementDialog(data) {
      this.XYOffsetData = data;
    },
    arrowKeyPressed(e) {
      console.log(this.tasks)
      let pageIdx = this.nowCropper.pageIdx;
      if (e === 1) {
        if (pageIdx === this.allTaskData[this.nowCropper.taskId].records.length) {
          // this.nowCropper.pageIdx = 1;
          // 判断是不是最后一面
          if (this.nowCropper.taskId === this.tasks[this.tasks.length - 1].id) {
            // 到头之后不再重新
            this.$message.warning('已经是最后一面的最后一页');
            this.warningText = '已经是最后一面的最后一页'
            setTimeout(() => {
              this.warningText = ''
            }, 2000)
          } else {
            this.nowCropper.taskId = this.tasks[this.tasks.findIndex((item) => item.id === this.nowCropper.taskId) + 1].id;
            this.nowCropper.pageIdx = 1;
            this.nowCropper.paperIdx = 0;

            this.$message.success('已切换到下一面');
            this.successText = '已切换到下一面'
            setTimeout(() => {
              this.successText = ''
            }, 2000)
          }
        } else {
          this.nowCropper.pageIdx = e + pageIdx;
        }
      } else if (e === -1) {
        if (pageIdx === 1) {
          // 到头之后不再重新
          this.$message.warning('已经是第一页');
          this.warningText = '已经是第一页'
          setTimeout(() => {
            this.warningText = ''
          }, 2000)
        } else {
          this.nowCropper.pageIdx = e + pageIdx;
        }
      }
    },
    previewSettingDialog(row) {
      switch (row.key) {
        case 'stats':
          this.$refs.statsDownloadForm.show(this.localFileTableData[0].args, this.calcScore().split('/')[1]);
          break
        case 'reviewWithOrig':
          this.$refs.downloadForm.show(this.localFileTableData[1].args, this.allTaskData[this.tasks[0].id].config.id, this.allTaskData.tasks.length)
          break
        case 'reviewWithoutOrig':
          this.$refs.downloadForm.show(this.localFileTableData[2].args, this.allTaskData[this.tasks[0].id].config.id, this.allTaskData.tasks.length)
          break
        case 'originPaper':
          this.$meesage.warning("该类型不支持配置")
          break;
      }
    },
    async downloadOrigin() {
      if (!this.isExportBatch) {
        if ('url' in this.allTaskData.fileDetail && this.allTaskData.fileDetail.url !== undefined) {
          if (this.dontNeedSaveFile.originPaper) {
            this.$axios.post('/api/remote/file/saveStaticFile', {
              save: true,
              path: this.folderPath,
              saveFileName: `${this.allTaskData.fileDetail.name}(原卷).pdf`,
              filename: this.allTaskData.fileDetail.url
            }).then((res) => {
              this.$message.success("保存成功");
            })
            this.dontNeedSaveFile.originPaper = false;
          } else {
            this.downloadFile(this.allTaskData.fileDetail.url, `${this.allTaskData.fileDetail.name}(原卷).pdf`);
          }
        } else {
          this.$message.warning("老版不支持下载原卷");
        }
      } else {
        for (let i = 0; i < this.localFileDetails.length; i++) {
          if ('url' in this.exportBatchFileDetails[this.localFileDetails[i].fileId].fileInfo && this.exportBatchFileDetails[this.localFileDetails[i].fileId].fileInfo.url !== undefined) {
            this.batchWaitingDownLoadFileList.originPaper.push({
              url: this.exportBatchFileDetails[this.localFileDetails[i].fileId].fileInfo.url,
              filename: `${this.localFileDetails[i].name}(原卷).pdf`
            });
          } else {
            this.$message.warning("老版不支持下载原卷");
          }
        }
      }

    },
    hasSwapPaper() {
      const taskIds = this.taskIds;
      for (const taskId of taskIds) {
        const records = this.allTaskData[taskId].records;
        for (const record of records) {
          if (record?.swapTargetRecordId) {
            return true;
          }
        }
      }
      return false;
    },
    // 点击预览
    handlePreview(row) {
      switch (row.key) {
        case 'stats':
          this.onStatsDownloadSubmit(this.localFileTableData[0].args)
          break
        case 'reviewWithOrig':
          this.onDownloadSubmit(this.localFileTableData[1].args)
          break
        case 'reviewWithoutOrig':
          this.onDownloadSubmit(this.localFileTableData[2].args)
          break
        case 'originPaper':
          this.downloadOrigin();
          break;
      }
    },
    async handlePrintConfirm(e) {
      const paperSelectorRef = this.isExportBatch ? this.$refs.otherPaperSelector : this.$refs.currentPaperSelector;
      if (this.isExportBatch) {
        const localFileDetails = paperSelectorRef.localFileDetails;
        this.localFileDetails = localFileDetails;
        const fileIds = localFileDetails.map(item => String(item.fileId));
        const response = await this.$axios.post(`/api/docCorrectFile/getTaskIdsByFileIds`, fileIds);
        this.batchWaitingDownLoadFileList.stats = [];
        this.batchWaitingDownLoadFileList.reviewWithOrig = [];
        this.batchWaitingDownLoadFileList.reviewWithoutOrig = [];
        this.batchWaitingDownLoadFileList.originPaper = [];
        this.exportBatchFileDetails = response.data;
      }
      let args = {};
      if (e) {
        // 远程打印
        this.dontNeedSaveFile.statisticalResults = true;
        this.dontNeedSaveFile.correctionIncludeTheOriginalPaper = true;
        this.dontNeedSaveFile.correctionDoesNotIncludeTheOriginalPaper = true;
        this.dontNeedSaveFile.originPaper = true;
        args = {
          remoteFileProps: {
            save: true,
            path: this.folderPath
          }
        }
      }

      if (this.isExportBatch && e) {
        // 批量+远程，首先创建文件夹
        for (let i = 0; i < this.localFileDetails.length; i++) {
          let loadingMessage = this.$message({
            message: `正在创建文件夹${this.localFileDetails[i].folderName}...`,
            icon: "Loading",
            type: "warning",
            duration: 0,
          })
          const response = await this.$axios.get(`/api/remote/file/createFolder?path=${this.folderPath}&folderName=${this.localFileDetails[i].folderName}`);
          loadingMessage.close();
        }
        this.save2RemoteSuccessCnt = 0;
        const checkedCount = this.localFileTableData.filter(row => row.checked).length;
        const paperCount = this.localFileDetails.length;
        const total = checkedCount * paperCount;
        let save2RemoteProgressMessageToast = this.$message({
          message: `已完成${this.save2RemoteSuccessCnt}/${total}个...`,
          icon: "Loading",
          type: "warning",
          duration: 0,
        })
        const save2RemoteProgressMessageInterval = setInterval(() => {
          save2RemoteProgressMessageToast.close()
          save2RemoteProgressMessageToast = this.$message({
            message: `已完成${this.save2RemoteSuccessCnt}/${total}个...`,
            icon: "Loading",
            type: "warning",
            duration: 0,
          })
          if (this.save2RemoteSuccessCnt === total) {
            save2RemoteProgressMessageToast.close();
            this.$message({
              message: `全部保存完成`,
              type: "success",
              duration: 10000
            })
            clearInterval(save2RemoteProgressMessageInterval);
          }
        }, 2000)
      }

      this.localFileTableData.forEach((row) => {
        if (row.checked) {
          switch (row.key) {
            case 'stats':
              this.onStatsDownloadSubmit({
                ...this.localFileTableData[0].args,
                ...args
              })
              break
            case 'reviewWithOrig':
              this.onDownloadSubmit({
                ...this.localFileTableData[1].args,
                ...args,
              })
              break
            case 'reviewWithoutOrig':
              this.onDownloadSubmit({
                ...this.localFileTableData[2].args,
                ...args
              })
              break
            case 'originPaper':
              this.downloadOrigin();
              break
            default:
              break
          }
        }
      })

      if (!e && this.isExportBatch) {
        // 非远程打印，检查各个格式的是否保存完毕，保存为则开始依次下载
        let loadingMessage = this.$message({
          message: "正在生成文件待下载列表...",
          icon: "Loading",
          type: "warning",
          duration: 0,
        })
        const checkedCount = this.localFileTableData.filter(row => row.checked).length;
        const paperCount = this.localFileDetails.length;
        const checkInterval = setInterval(async () => {
          if (this.isExportBatching) return;
          const { stats, reviewWithOrig, reviewWithoutOrig, originPaper } = this.batchWaitingDownLoadFileList;
          const total = stats.length + reviewWithOrig.length + reviewWithoutOrig.length + originPaper.length;

          loadingMessage.close();
          loadingMessage = this.$message({
            message: `正在生成文件待下载列表${total}/${checkedCount * paperCount}...`,
            icon: "Loading",
            type: "warning",
            duration: 0,
          })

          if (checkedCount * paperCount === total) {
            this.isExportBatching = true;
            loadingMessage.close();

            // 开始下载, 有个总的进度
            let totalLoadingMessage = this.$message({
              message: `已下载 0/${total}个`,
              icon: "Loading",
              type: "warning",
              duration: 0,
            })
            let successCnt = 0;
            for (let i = 0; i < paperCount; i++) {
              totalLoadingMessage.close();
              totalLoadingMessage = this.$message({
                message: `已下载 ${successCnt}/${total}个`,
                icon: "Loading",
                type: "warning",
                duration: 0,
              })
              if (i < stats.length) {
                let loadingMessage = this.$message({
                  message: `正在下载${stats[i].filename}...`,
                  icon: "Loading",
                  type: "warning",
                  duration: 0,
                })
                await this.downloadFile(stats[i].url, stats[i].filename);
                loadingMessage.close();
                successCnt++;
              }
              await delay(100);
              if (i < reviewWithOrig.length) {
                let loadingMessage = this.$message({
                  message: `正在下载${reviewWithOrig[i].filename}...`,
                  icon: "Loading",
                  type: "warning",
                  duration: 0,
                })
                await this.downloadFile(reviewWithOrig[i].url, reviewWithOrig[i].filename);
                loadingMessage.close();
                successCnt++;
              }
              await delay(100);
              if (i < reviewWithoutOrig.length) {
                let loadingMessage = this.$message({
                  message: `正在下载${reviewWithoutOrig[i].filename}...`,
                  icon: "Loading",
                  type: "warning",
                  duration: 0,
                })
                await this.downloadFile(reviewWithoutOrig[i].url, reviewWithoutOrig[i].filename);
                loadingMessage.close();
                successCnt++;
              }
              await delay(100);
              if (i < originPaper.length) {
                let loadingMessage = this.$message({
                  message: `正在下载${originPaper[i].filename}...`,
                  icon: "Loading",
                  type: "warning",
                  duration: 0,
                })
                await this.downloadFile(originPaper[i].url, originPaper[i].filename);
                loadingMessage.close();
                successCnt++;
              }
              await delay(100);
            }
            totalLoadingMessage.close();
            this.$message({
              message: `全部下载完成`,
              type: "success",
              duration: 10000
            })
            this.isExportBatching = false;
            clearInterval(checkInterval);
          }
        }, 1000);
      }
      this.printDialogVisible = false
    },
    currentChange(e) {
      this.$refs.cropper.setImg(this.base64Images[e - 1]);
      this.preview();
    },
    changeTaskId(e) {
      // 切换试卷列表，如果没有则展开，然后渲染
      if (this.paginationTaskId in this.allTaskData) {
        // 已经加载过了,直接渲染
        let needRefresh = this.nowCropper.pageIdx === 1;
        this.nowCropper.taskId = this.paginationTaskId;
        this.nowCropper.pageIdx = 1;
        this.nowCropper.paperIdx = 0;
        this.nowCropper.docUrl = this.allTaskData[this.paginationTaskId].records[0].docurl;
        if (needRefresh) {
          this.initCropper()
        }
      } else {
        this.changePaginationLoading = true;
      }
    },
    reviewTask(options) {
      this.nowCropper.paperIdx = options.paperIdx;
      this.nowCropper.pageIdx = options.paperIdx + 1;
      this.nowCropper.docUrl = options.docUrl;
      this.nowCropper.taskId = options.taskId;
    },
    errorCorrectionFromCropper(rightIdx, score = null, clickIndexScorePoint = null) {
      this.errorCorrectionFromTable(this.nowCropper.taskId, this.nowCropper.paperIdx, rightIdx, score)
    },
    errorCorrectionFromTable(taskId, recordIdx, rightIdx, score = null) {
      let areas = this.allTaskData[taskId].records[recordIdx].reviewedObj;
      let configAreasObj = this.allTaskData[taskId].config.areasObj;
      // 根据qsIdx计算出位置
      let idx = 0;
      let questionData = null
      let areaIdx = 0
      let qsIdx = 0;
      for (; areaIdx < configAreasObj.length; areaIdx++) {
        const questions = areas[areaIdx].reviewed;
        for (qsIdx = 0; qsIdx < configAreasObj[areaIdx].questions.length; qsIdx++) {
          if (idx === rightIdx) {
            questionData = questions[qsIdx];
            questionData.areaType = configAreasObj[areaIdx].areaType;
            break;
          }
          idx++;
        }
        if (questionData) break;
      }
      if (!questionData) {
        return this.$message.error('没有找到该题目');
      }
      this.onCorrect(questionData, taskId, recordIdx, areaIdx, qsIdx, rightIdx, score)
    },
    onClickErrorCorrection(reviewed, taskId, recordIdx, areaIdx, qsIdx, docurl, rightIdx, hasChange = 1) {
      const key = `${taskId}_${recordIdx}_${areaIdx}_${qsIdx}`;
      if (this.debounceTimers[key]) {
        clearTimeout(this.debounceTimers[key]);
      }
      this.debounceTimers[key] = setTimeout(() => {
        const refs = this.$refs[taskId];
        if (refs && refs.length) {
          const lastRef = refs[refs.length - 1];
          const detail = this.cropperDetail[taskId]
            .qsPositionDetail[docurl]
            .detail[rightIdx];
          lastRef.errorCorrectionRefresh(
            reviewed,
            taskId,
            recordIdx,
            rightIdx,
            detail,
            areaIdx,
            qsIdx,
            hasChange
          );
        }
        delete this.debounceTimers[key];
      }, 1000);
    },
    updateQs(updated, taskId, recordIdx, areaIdx, qsIdx, rightIdx) {
      console.log('updated', updated)
      const taskDetail = this.allTaskData[taskId];
      let reviewed = taskDetail.records[recordIdx].reviewedObj
      let configQs = taskDetail.config.areasObj[areaIdx].questions[qsIdx] || {};
      let configArea = taskDetail.config.areasObj[areaIdx] || {};
      const docurl = taskDetail.records[recordIdx].docurl;
      // 先更新表格和cropperDetail
      this.allTaskData[taskId].records[recordIdx].reviewedObj[areaIdx].reviewed[qsIdx] = updated;
      this.cropperDetail[taskId].qsPositionDetail[docurl].detail[rightIdx].isRight = updated.isCorrect === 'Y';
      if (updated.isScorePoint === 2) {
        this.cropperDetail[taskId].qsPositionDetail[docurl].detail[rightIdx].scored = updated.scored;
      } else {
        this.cropperDetail[taskId].qsPositionDetail[docurl].detail[rightIdx].scored = updated.isCorrect === 'Y' ? this.cropperDetail[taskId].qsPositionDetail[docurl].detail[rightIdx].fullMarks : 0;
      }
      reviewed[areaIdx].reviewed[qsIdx] = Object.assign({}, reviewed[areaIdx].reviewed[qsIdx], updated)
      this.errorCorrectionRefresh(reviewed, taskId, recordIdx, rightIdx, this.cropperDetail[taskId].qsPositionDetail[docurl].detail[rightIdx]);
      let hasChange = false;
      reviewed.forEach((area) => {
        area.reviewed.forEach((qs) => {
          hasChange = hasChange || qs?.hasChange
        })
      })
      // 加一个防抖，同一个taskId, recordIdx, areaIdx, qsIdx的防止用户连续点击
      this.onClickErrorCorrection(reviewed, taskId, recordIdx, areaIdx, qsIdx, docurl, rightIdx, hasChange);
      let form = {
        id: taskDetail.records[recordIdx].id,
        hasChange: hasChange ? 1 : 0,
        reviewed: JSON.stringify(reviewed)
      }
      this.allTaskData[taskId].records[recordIdx].hasChange = hasChange;
      this.hasChange = hasChange ? '已修改' : '未修改';
      this.$axios.post("/api/docCorrectRecord/update", form).then(res => {
        this.$message.success("更新成功");
      })

      // let data = {
      //   recordId: taskDetail.records[recordIdx].id,
      //   taskId: taskId,
      //   fileId: this.fileId,
      //   configId: taskDetail.config.id,
      //   docName: taskDetail.records[recordIdx].docname,
      //   areaIdx: areaIdx,
      //   qsIdx: qsIdx,
      //   afterErrorCorrectionScore: updated.scored,
      //   beforeErrorCorrectionScore: reviewed[areaIdx].reviewed[qsIdx].scored,
      //   afterErrorCorrectionAnswer: updated.isCorrect,
      //   beforeErrorCorrectionAnswer: reviewed[areaIdx].reviewed[qsIdx].isCorrect,
      //   rightAnswer: configQs.answer,
      //   studentAnswer: reviewed[areaIdx].reviewed[qsIdx].studentAnswer,
      //   areaType: configArea.areaType,
      //   areaImgUrl: reviewed[areaIdx].areaImg,
      //   detail: JSON.stringify({
      //     configQs: configQs,
      //     configArea: configArea,
      //     reviewedArea: reviewed[areaIdx],
      //     reviewedQs: reviewed[areaIdx].reviewed[qsIdx],
      //     qsOcrPrompt: taskDetail.config.configObj.qsOcrPrompt
      //   })
      // }
      // this.$axios.post("/api/errorCorrection/add", data).then(res => {
      // });

      // setTimeout(() => {
      //   this.loadFile()
      // }, 2000);
    },
    onCorrect(data, taskId, recordIdx, areaIdx, qsIdx, rightIdx, score = null) {
      if (!data) return;
      if (data.areaType === 4) {
        if (score === null || score === undefined) {
          // 非全屏模式-用户手动输入分数
          this.$prompt('请输入新的分数', '修改分数', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputPattern: /^[0-9]+(\.[0-9]{1,2})?$/, // 正则，确保是数字且最多两位小数
            inputErrorMessage: '请输入有效的分数（数字，最多两位小数）'
          }).then(({ value }) => {
            // 用户确认输入
            if (value !== '') {
              if (!('isCorrectOriginal' in data)) {
                // 首次纠错 要保存原始答案和分数，方便判断hasChange
                data.isCorrectOriginal = data.isCorrect
                data.scoredOriginal = Number(data?.scored);
              }
              data.hasChange = (Number(value) === Number(data.scoredOriginal)) ? 0 : 1
              data.scored = Number(value)
              this.updateQs(data, taskId, recordIdx, areaIdx, qsIdx, rightIdx); // 更新问题
            }
          }).catch((e) => {
            // 用户点击取消时保持原分数不变
            this.$message.info("已取消修改");
          });
        } else {
          // 全屏模式，传过来分数，直接应用修改
          if (!('isCorrectOriginal' in data)) {
            // 首次纠错 要保存原始答案和分数，方便判断hasChange
            data.isCorrectOriginal = data.isCorrect
            data.scoredOriginal = Number(data?.scored);
          }
          data.hasChange = Number(score) === Number(data.scoredOriginal) ? 0 : 1
          data.scored = Number(score)
          // 更新分数
          data.scored = Number(score); // 更新分数
          data.hasChange = 1;
          this.updateQs(data, taskId, recordIdx, areaIdx, qsIdx, rightIdx); // 更新问题
        }
      } else {
        if (!('isCorrectOriginal' in data)) {
          // 首次纠错 要保存原始答案和分数，方便判断hasChange
          data.isCorrectOriginal = data.isCorrect
          if (data?.isScorePoint === 2) {
            data.scoredOriginal = data.scored;
          }
        }
        data.isCorrect = data.isCorrect === 'Y' ? 'N' : 'Y'
        data.hasChange = (data.isCorrect === data.isCorrectOriginal) ? 0 : 1

        this.updateQs(data, taskId, recordIdx, areaIdx, qsIdx, rightIdx)
      }
    },
    errorCorrectionRefresh(data, taskId, recordIdx, rightIdx, updated) {
      // let options = data.options;
      // 如果是当前页就更新
      if (this.nowCropper.taskId === taskId && this.nowCropper.paperIdx === recordIdx) {
        // 更新当前页
        // const data = Object.assign({
        //   color: 'green',
        //   mark: document.getElementById((updated?.isScorePoint === 2 && updated.fullMarks !== updated.scored) ?
        //       'banduiImg' : (updated.isRight ? (updated?.credibility ?? 1 > 0.5 ? "flagimg" : 'flagimgWarn') : (updated?.credibility ?? 1 > 0.5 ? "wrongImg" : "wrongImgWarn"))),
        //   isScorePoint: updated.isScorePoint,
        //   text: updated.answer === '无' ? updated.scored : updated.answer,
        // }, updated.flagArea);
        //
        // const answer = {
        //   area: updated.flagArea,
        //   text: updated.answer === '无' ? (updated.scored + (updated.isScorePoint === 2 ? `/${updated.fullMarks}` : '')) : updated.answer,
        //   dontShowText: updated?.dontShowText ?? false
        // }
        // this.$refs.cropper.errorCorrectionRefresh(rightIdx, data, answer);
        this.preview(true)
      }
    },
    async loadNextImage(nextDocUrl, nextNextDocUrl) {
      if (!nextDocUrl) return;
      if (nextDocUrl in this.imagesList) return;
      if (this.loadingImagesList.includes(nextDocUrl)) return;
      try {
        this.loadingImagesList.push(nextDocUrl);
        const response = await fetch(this.$fileserver.fileurl(nextDocUrl));
        this.loadingImagesList = this.loadingImagesList.filter(item => item !== nextDocUrl);
        if (!response.ok) {
          throw new Error('Failed to fetch PDF');
        }

        const blob = await response.blob();
        const pdfData = await blob.arrayBuffer();
        const pdfDoc = await pdfjsLib.getDocument(pdfData).promise;
        const totalPages = pdfDoc.numPages;
        const images = [];
        const targetDPI = 300;
        const scaleFactor = targetDPI / 72;  // PDF 默认分辨率通常是 72 DPI

        if (totalPages === 1) {
          const page = await pdfDoc.getPage(1);
          const canvas = document.createElement('canvas');
          const context = canvas.getContext('2d');
          const viewport = page.getViewport({ scale: scaleFactor });
          canvas.width = viewport.width;
          canvas.height = viewport.height;

          // 将页面渲染到 canvas 上
          await page.render({
            canvasContext: context,
            viewport: viewport,
          }).promise;

          // 将 canvas 转换为 Base64 编码的图片（JPEG 格式）
          const imgDataUrl = canvas.toDataURL('image/jpeg');
          images.push(imgDataUrl);

          const keys = Object.keys(this.imagesList);
          if (keys.length > 10) {
            const firstKey = keys[0];
            delete this.imagesList[firstKey];
          }
          this.imagesList[nextDocUrl] = imgDataUrl;
        }
        // 演示更新
        setTimeout(() => {
          this.loadNextImage(nextNextDocUrl, '');
        }, 500)
      } catch (error) {
        console.error('加载下一个图像失败', error);
      }
    },
    async convertPdfUrlToBase64(pdfUrl, nextDocUrl, nextNextDocUrl) {
      if (this.isRefreshingDataAndDontNeedInitCropper) return;
      this.loadingCropper = true;
      console.log('start download pdf', pdfUrl, nextDocUrl, nextNextDocUrl);
      try {
        // 通过 fetch 下载 PDF 文件
        if (pdfUrl in this.imagesList) {
          this.imgDataUrl = this.imagesList[pdfUrl];
          this.loadingCropper = false;
          this.preview();
          this.loadNextImage(nextDocUrl, nextNextDocUrl)
        } else {
          const response = await fetch(this.$fileserver.fileurl(pdfUrl));
          // 异步提前缓存下一个
          this.loadNextImage(nextDocUrl, nextNextDocUrl)
          if (!response.ok) {
            throw new Error('Failed to fetch PDF');
          }
          const blob = await response.blob();
          const pdfData = await blob.arrayBuffer();
          const pdfDoc = await pdfjsLib.getDocument(pdfData).promise;

          // 获取 PDF 的总页数
          const totalPages = pdfDoc.numPages;
          const images = [];

          // 设置目标 DPI（300 DPI）
          const targetDPI = 300;

          // 获取每英寸的像素数，假设 PDF 使用标准的 72 DPI
          const scaleFactor = targetDPI / 72;  // PDF 默认分辨率通常是 72 DPI

          // 遍历每一页，将其转换为 Base64 图像
          if (totalPages === 1) {
            const page = await pdfDoc.getPage(1);

            // 创建一个 canvas 元素
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');

            // 获取页面的渲染视口
            const viewport = page.getViewport({ scale: scaleFactor });

            // 设置 canvas 大小为页面大小
            canvas.width = viewport.width;
            canvas.height = viewport.height;

            // 将页面渲染到 canvas 上
            await page.render({
              canvasContext: context,
              viewport: viewport,
            }).promise;
            const imgDataUrl = canvas.toDataURL('image/jpeg');
            images.push(imgDataUrl);
            this.loadingCropper = false;
            this.imgDataUrl = imgDataUrl;
            this.base64Images = images;
            this.imagesList[pdfUrl] = imgDataUrl;
            this.preview();
          }
        }

      } catch (error) {
        console.error('PDF 转换失败', error);
      }
    },
    async convertSinglePdfUrlToBase64() {
      if (this.haveLoadSinglePdf) return;
      this.haveLoadSinglePdf = true;
      let pdfUrl = this.allTaskData.fileDetail?.url;
      if (!pdfUrl) return;
      const response = await fetch(this.$fileserver.fileurl(pdfUrl));
      if (!response.ok) {
        throw new Error('Failed to fetch PDF');
      }
      const blob = await response.blob();
      const pdfData = await blob.arrayBuffer();
      const pdfDoc = await pdfjsLib.getDocument(pdfData).promise;
      const totalPages = pdfDoc.numPages;
      const taskLength = this.allTaskData.tasks.length;
      const taskIds = this.allTaskData.tasks.map(task => task.id);
      const targetDPI = 300;
      const scaleFactor = targetDPI / 72;
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      const batchSize = 5;
      const tempMap = {};
      this.paperSize = totalPages;

      // 遍历每一页，将其转换为 Base64 图像
      for (let i = 1; i <= totalPages; i++) {
        if (this.stopConvertSinglePdfUrlToBase64) {
          this.paperSize = 0;
          continue;
        }
        console.log('i', taskIds[(i - 1) % taskLength], Math.floor((i - 1) / taskLength))
        let paperUrl = this.allTaskData[taskIds[(i - 1) % taskLength]].records[Math.floor((i - 1) / taskLength)].docurl;
        if (paperUrl in this.imagesList) {
          continue;
        }
        const page = await pdfDoc.getPage(i);
        const viewport = page.getViewport({ scale: scaleFactor });
        canvas.width = viewport.width;
        canvas.height = viewport.height;
        await page.render({
          canvasContext: context,
          viewport: viewport,
        }).promise;
        // 每页渲染完后"让渡"一下：0ms 可以让出主线程到下一轮事件循环
        await new Promise(resolve => setTimeout(resolve, 0));
        tempMap[paperUrl] = canvas.toDataURL('image/jpeg');


        if (i % batchSize === 0 || i === totalPages) {
          Object.assign(this.imagesList, tempMap);
          for (const key in tempMap) delete tempMap[key];
        }
        this.convertIdx = i;
        // 让其更新的慢一点不然太卡了
      }
      this.convertIdx = 0;
    },
    preview(lightUpdate = false) {
      if (!this.nowCropper.docUrl) {
        this.nowCropper.docUrl = this.allTaskData[this.nowCropper.taskId].records[this.nowCropper.paperIdx].docurl
      }
      this.addErrorCorrection()
      let detail = this.cropperDetail[this.nowCropper.taskId].qsPositionDetail[this.nowCropper.docUrl].detail;
      let answers = [];
      let areas = [];
      let kuangs = [];
      let scopeTypes = ['总分'];
      let scopeTypesAllTask = ['总分'];
      let scopeTypesScore = {
        '总分': { scored: 0.0, fullMarks: 0.0 }
      };
      let scopeTypesScoreAllTask = {
        '总分': { scored: 0.0, fullMarks: 0.0 }
      };
      let idx = 0;
      detail.forEach(area => {
        if (area.flagArea) {
          areas.push(Object.assign({
            color: 'green',
            mark: document.getElementById((area?.isScorePoint === 2 && area.fullMarks !== area.scored) ?
              'banduiImg' : (area.isRight ? (area?.credibility ?? 1 > 0.5 ? "flagimg" : 'flagimgWarn') : (area?.credibility ?? 1 > 0.5 ? "wrongImg" : "wrongImgWarn"))),
            isScorePoint: area.isScorePoint,
            text: area.answer === '无' ? area.scored : area.answer,
          }, area.flagArea))
          answers.push({
            area: area.flagArea,
            text: area.answer === '无' ? (area.scored + (area.isScorePoint === 2 ? `/${area.fullMarks}` : '')) : area.answer,
            dontShowText: area?.dontShowText ?? false,
          })
        }
        if (!scopeTypes.includes(area.scoreType)) {
          scopeTypes.push(area.scoreType)
        }
        let scored = Number(area?.scored ?? 0.0);
        let fullMarks = Number(area?.fullMarks ?? 0.0);
        if (area.scoreType !== '总分') {

          if (!(area.scoreType in scopeTypesScore)) {
            scopeTypesScore[area.scoreType] = { scored: 0.0, fullMarks: 0.0 }
          }
          scopeTypesScore[area.scoreType].scored += scored;
          scopeTypesScore[area.scoreType].fullMarks += fullMarks;
        }
        scopeTypesScore['总分'].scored += scored;
        scopeTypesScore['总分'].fullMarks += fullMarks;
      })
      scopeTypesScoreAllTask = scopeTypesScore;
      scopeTypesAllTask = scopeTypes;
      for (let i = 0; i < this.allTaskData.tasks.length; i++) {
        let taskId = this.allTaskData.tasks[i].id;
        if (taskId !== this.nowCropper.taskId) {
          let docUrl = this.allTaskData[taskId].records[this.nowCropper.paperIdx]?.docurl;
          if (!docUrl) {
            continue;
          }
          let detail = this.cropperDetail[taskId].qsPositionDetail[docUrl].detail;
          detail.forEach(area => {
            if (!scopeTypesAllTask.includes(area.scoreType)) {
              scopeTypesAllTask.push(area.scoreType)
            }
            let scored = Number(area?.scored ?? 0.0);
            let fullMarks = Number(area?.fullMarks ?? 0.0);
            if (area.scoreType !== '总分') {

              if (!(area.scoreType in scopeTypesScoreAllTask)) {
                scopeTypesScoreAllTask[area.scoreType] = { scored: 0.0, fullMarks: 0.0 }
              }
              scopeTypesScoreAllTask[area.scoreType].scored += scored;
              scopeTypesScoreAllTask[area.scoreType].fullMarks += fullMarks;
            }
            scopeTypesScoreAllTask['总分'].scored += scored;
            scopeTypesScoreAllTask['总分'].fullMarks += fullMarks;
          })
        }
      }
      // 将scoreType的总分调整到最后
      scopeTypes = [...scopeTypes.slice(1), scopeTypes[0]];
      scopeTypesAllTask = [...scopeTypesAllTask.slice(1), scopeTypesAllTask[0]];

      // 姓名/学号也展示文字
      const record = this.allTaskData[this.nowCropper.taskId].records[this.nowCropper.paperIdx];
      const configObj = this.allTaskData[this.nowCropper.taskId].config.configObj;
      if (configObj.nameArea && 'x' in configObj.nameArea) {
        answers.push({
          area: configObj.nameArea,
          text: record.identify || '无',
          fillStyle: 'blue',
          type: 'identify',
          offsetX: 80,
          offsetY: 20,
          font: '160px Arial'
        })

        kuangs.push({
          area: configObj.nameArea,
          type: 'identify',
          color: '#8A2BE2'
        })
      }
      if (configObj.studentNumberArea && 'x' in configObj.studentNumberArea) {
        answers.push({
          area: configObj.studentNumberArea,
          text: record.studentNumber || '无',
          fillStyle: 'blue',
          type: 'studentNumber',
          offsetX: 80,
          offsetY: 20,
          font: '160px Arial'
        })

        kuangs.push({
          area: configObj.studentNumberArea,
          type: 'studentNumber',
          color: '#8A2BE2'
        })
      }
      // 等级
      if (configObj.totalScoreArea && 'x' in configObj.totalScoreArea) {
        // kuangs.push({
        //   area: configObj.totalScoreArea,
        //   color: '#e8a3a3',
        //   type: 'totalScoreArea',
        // })
        // 等级输出
        const ranges = this.getRanges();
        let text = "    ";
        for (let i = 0; i < scopeTypesAllTask.length; i++) {
          let rangeItem = this.judgeRange(scopeTypesAllTask[i], scopeTypesScoreAllTask[scopeTypesAllTask[i]].scored, ranges)
          text += `${rangeItem}-${scopeTypesScoreAllTask[scopeTypesAllTask[i]].fullMarks - scopeTypesScoreAllTask[scopeTypesAllTask[i]].scored}   `;
        }
        answers.push({
          area: {
            x: configObj.totalScoreArea.x,
            y: configObj.totalScoreArea.y,
            width: 1,
            height: 1
          },
          text: text,
          fillStyle: 'red',
          type: 'totalScoreAreaText',
          offsetX: 1,
          offsetY: 1,
          font: 'italic 120px Arial'
        })
      }
      if (configObj.score && configObj.scoreArea && 'x' in configObj.scoreArea) {
        // kuangs.push({
        //   area: configObj.scoreArea,
        //   color: '#e8a3a3',
        //   type: 'scoreArea',
        // })
        // 现在有三种类型
        let text = "";
        const scoreFormat = configObj.scoreFormat;
        if (scoreFormat === 6) {
          let totalScore = 0.0;
          for (let i = 0; i < scopeTypes.length; i++) {
            text += `${scopeTypesScore[scopeTypes[i]].scored} `;
          }
        } else if (scoreFormat === 7) {
          for (let i = 0; i < scopeTypes.length; i++) {
            text += `-${scopeTypesScore[scopeTypes[i]].fullMarks - scopeTypesScore[scopeTypes[i]].scored} `;
          }
        } else {
          for (let i = 0; i < scopeTypes.length; i++) {
            text += `${scopeTypes[i]}:${scopeTypesScore[scopeTypes[i]].scored}/${scopeTypesScore[scopeTypes[i]].fullMarks}`;
          }
        }

        answers.push({
          area: {
            x: configObj.scoreArea.x,
            y: configObj.scoreArea.y,
            width: 1,
            height: 1
          },
          text: text,
          fillStyle: 'red',
          type: 'scoreAreaText',
          offsetX: 1,
          offsetY: 1,
          font: '85px Arial'
        })
      }

      // 画框
      const configAreas = this.allTaskData[this.nowCropper.taskId].config.areasObj;
      if (configAreas && configAreas.length > 0) {
        kuangs.push(...configAreas.map((item, itemIndex) => {
          return {
            area: item.area,
            color: item.areaType === 4 ? 'red' : '',
            status: record.reviewedObj?.[itemIndex]?.status
          }
        }))
      }
      if (lightUpdate) {
        this.$refs.cropper.lightUpdate(areas, answers, kuangs);
      } else {
        this.$refs.cropper.setImg(this.imgDataUrl, areas, answers, kuangs, false, this.forceAdjust);
      }

      this.forceAdjust = false;
    },
    judgeRange(scopeType, scored, ranges) {
      if (!ranges) return '无';
      if (scopeType in ranges) {
        const rangesItem = ranges[scopeType];
        for (let i = 0; i < rangesItem.length; i++) {
          if (scored >= rangesItem[i].minn && scored <= rangesItem[i].maxx) {
            return rangesItem[i].name;
          }
        }
        return '无'
      } else {
        return '无'
      }
    },
    loadCropperDetail(records, config, taskId) {
      // url的list
      let docList = [];
      records.forEach(record => {
        docList.push(record.docurl)
      })
      // 用于打勾叉的数据
      let data = {};
      records.forEach(record => {

        let detail = []
        for (let i = 0; i < record.reviewedObj.length; i++) {
          let areaIdx = record.reviewedObj[i].areaIdx;
          for (let qsIdx = 0; qsIdx < record.reviewedObj[i].reviewed.length; qsIdx++) {
            if (
              config &&
              config.areasObj &&
              Array.isArray(config.areasObj) &&
              config.areasObj[areaIdx] &&
              config.areasObj[areaIdx].questions &&
              qsIdx < config.areasObj[areaIdx].questions.length &&
              'flagArea' in config.areasObj[areaIdx].questions[qsIdx]
            ) {
              let scored = 0;
              if (record.reviewedObj[i].reviewed[qsIdx].isScorePoint === 2) {
                scored = record.reviewedObj[i].reviewed[qsIdx]?.scored ?? 0
              } else {
                scored = record.reviewedObj[i].reviewed[qsIdx].isCorrect === 'Y' ? config.areasObj[areaIdx].questions[qsIdx]?.score ?? 0 : 0
              }
              detail.push({
                areaIdx: areaIdx,
                qsIdx: qsIdx,
                isRight: record.reviewedObj[i].reviewed[qsIdx].isCorrect === 'Y',
                flagArea: config.areasObj[areaIdx].questions[qsIdx].flagArea,
                answer: record.reviewedObj[i].reviewed[qsIdx].isEssay ? record.reviewedObj[i].reviewed[qsIdx].scored : config.areasObj[areaIdx].questions[qsIdx].answer,
                isEssay: record.reviewedObj[i].reviewed[qsIdx].isEssay,
                scored: scored,
                isScorePoint: record.reviewedObj[i].reviewed[qsIdx].isScorePoint,
                dontShowText: config.areasObj[areaIdx].questions[qsIdx]?.dontShowText ?? false,
                scoreType: config.areasObj[areaIdx].questions[qsIdx]?.scoreType ?? '总分',
                fullMarks: config.areasObj[areaIdx].questions[qsIdx]?.score ?? 0,
                credibility: record.reviewedObj[i].reviewed[qsIdx]?.credibility ?? 1,
              })
            }
          }
        }
        data[record.docurl] = {
          docUrl: record.docurl,
          id: record.id,
          detail: detail
        }
      })
      // area的配置信息
      this.loadRecordFinish({
        docList: docList,
        data: data,
        areaConfig: config.areasObj,
        taskId: taskId
      });
    },
    loadRecordFinish(data) {
      this.cropperDetail[data.taskId] = {
        docUrlList: data.docList,
        qsPositionDetail: data.data,
        areaConfig: data.areaConfig
      }
      if (!this.nowCropper.taskId || this.changePaginationLoading) {
        let needCropper = false;
        if (this.changePaginationLoading) {
          // 由用户切换套卷触发的也要初始化0
          this.changePaginationLoading = false;
          if (!this.nowCropper.pageIdx || this.nowCropper.pageIdx === 1) {
            // 需要手动触发
            needCropper = true;
          }
        }
        // 当前没有选中任何，要初始化0
        this.nowCropper.paperIdx = 0;
        this.nowCropper.pageIdx = 1;
        this.nowCropper.taskId = data.taskId;
        this.nowCropper.docUrl = data.docList[0];
        this.paginationTaskId = data.taskId;
        if (needCropper) this.initCropper();
      }

      if (this.firstLoadPdf) {
        const nowCropperByFileIds = JSON.parse(JSON.stringify(store.getNowCropperByFileIds(this.fileId)));
        if (nowCropperByFileIds) {
          this.nowCropper = nowCropperByFileIds;
        }
        this.firstLoadPdf = false;
      }
    },
    onExpandChange(row, expandRows) {
      // 如果是新展开的
      if (this.expandRows.findIndex(item => item === row.id) === -1) {
        this.$nextTick(() => {
          this.nowCropper.taskId = row.id;
        })
      }

      this.expandRows = expandRows.map(r => r.id);

    },
    onEditTask(data) {
      this.isFreezeKeyboardTempSave = this.isFreezeKeyboard;
      this.isFreezeKeyboard = true;
      this.$refs.taskForm.show({ title: "编辑", data });
    },
    onDeleteTask(id) {
      this.$confirm("确认删除该任务吗？", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$axios.post(`/api/docCorrectTask/delete?id=${id}`).then(res => {
          this.$message.success("删除成功");
          this.loadFile();
        });
      });
    },
    refresh() {
      this.loadFile();
    },
    initTasks(tasks) {
      let records = tasks;
      if (tasks.length) {
        this.tasks = tasks;

        let hasCorrecting = tasks.find(task => task.status === 2);
        if (hasCorrecting) {
          if (!this.timer) {
            this.timer = setInterval(() => {
              if (this.firstLoadNeedInitCropper) {
                this.isRefreshingDataAndDontNeedInitCropper = true;
                this.firstLoadNeedInitCropper = false;
                setTimeout(() => {
                  this.isRefreshingDataAndDontNeedInitCropper = false;
                }, 1000);
              }
              this.loadFile();
            }, 5 * 1000);
          }
        } else {
          this.isRefreshingDataAndDontNeedInitCropper = false;
          if (this.timer) {
            clearInterval(this.timer);
          }
          this.timer = null;
          // 删除进度一栏
          this.columns = this.columns.filter(item => item.prop !== 'progress');
        }


        // 没批改的自动批改
        if (!this.autoCorrect) {
          let haveOne2Correct = false;
          tasks.forEach(task => {
            if (task.status === 1 && task.configId) {
              haveOne2Correct = true;
              // this.correctTask(task.id);
            }
          })
          if (haveOne2Correct) {
            this.$axios.post(`/api/docCorrectFile/correct`, {
              id: this.fileId,
              modelValue: this.file.modelValue
            }).then(res => {
              this.timer = setTimeout(() => {
                this.loadFile()
              }, 1000)
            })
          }
          this.autoCorrect = true;
        }
      } else {
        this.$message.info("暂无数据");
      }
    },
    initCropper() {
      console.log('initCropper', this.nowCropper);
      if (this.nowCropper.docUrl) {
        this.nowCropper.docUrl = this.allTaskData[this.nowCropper.taskId].records[this.nowCropper.paperIdx].docurl;
        let nextDocUrl = '';
        const nowRecordSize = this.allTaskData[this.nowCropper.taskId].records.length;
        if (this.nowCropper.paperIdx + 1 < nowRecordSize) {
          nextDocUrl = this.allTaskData[this.nowCropper.taskId].records[this.nowCropper.paperIdx + 1].docurl;
        }
        let nextNextDocUrl = '';
        if (this.nowCropper.paperIdx + 2 < nowRecordSize) {
          nextNextDocUrl = this.allTaskData[this.nowCropper.taskId].records[this.nowCropper.paperIdx + 2].docurl;
        }
        this.convertPdfUrlToBase64(this.nowCropper.docUrl, nextDocUrl, nextNextDocUrl).then(res => {
        })
      }
    },
    toRecord(id) {
      this.$router.push(`/docrecord/${id}`);
    },
    toConfig(configId) {
      let query = {}
      if (configId) {
        query.id = configId;
        query.packageId = this.allTaskData?.fileDetail?.configPackageId
      }
      this.$router.push({
        name: "docconfig",
        query,
      });
    },
    correctTask(taskId) {
      const tasks = this.allTaskData?.tasks ?? [];
      let form = {
        id: taskId,
        aimodel: tasks.filter(task => task.id === taskId).map(task => task.correctConfigObj?.aimodel ?? store.getDefaultCorrectModal)[0] ?? store.getDefaultCorrectModal,
        ocrType: '2',
        responseFormat: true,
        jsonobject: tasks.filter(task => task.id === taskId).map(task => task.correctConfigObj?.jsonobject ?? false)[0] ?? false,
        jsonschema: tasks.filter(task => task.id === taskId).map(task => task.correctConfigObj?.jsonschema ?? false)[0] ?? false,
      }
      this.$axios.post("/api/docCorrectTask/execute", form).then(res => {
        setTimeout(() => {
          this.refresh()
          this.$forceUpdate;
        }, 500)

        setTimeout(() => {
          this.refresh()
        }, 4500)
      })
    },
    reIdentifyNameOrStudentNumberType(taskId, type) {
      let form = {
        id: taskId,
        aimodel: this.useAimodel || store.getDefaultCorrectModal,
        ocrType: '2',
        responseFormat: true,
        reIdentifyNameOrStudentNumberType: type
      }
      this.$axios.post("/api/docCorrectTask/reIdentifyNameOrStudentNumberType", form).then(res => {
        setTimeout(() => {
          this.refresh()
          this.$forceUpdate;
        }, 500)

        setTimeout(() => {
          this.refresh()
        }, 4500)
      })
    },
    correctName(id) {
      this.$axios.post("/api/docCorrectTask/correctName", {
        id,
      }).then(res => {
        this.$message.success("校正中，请稍后查看...")
      })
    },
    doCorrectName(command, id) {
      if (command === 'sync') {
        // 同步另一张试卷的姓名
        this.$refs.taskSelector.show({
          title: "同步试卷姓名", data: {
            id,
          }
        })
      }
    },
    doSyncDocName(formData) {
      this.$message.success("同步中，请稍后查看...")
      this.$axios.post("/api/docCorrectTask/syncDocName", formData).then(() => {
        this.$message.success("同步成功")
      })
    },
    saveResult(taskId, rowIdx) {
      this.resultSaving[rowIdx] = true
      this.$axios.post(`/api/docCorrectResult/saveTask?taskId=${taskId}`).then(res => {
        this.$message.success("保存成功")
      }).finally(() => {
        this.resultSaving[rowIdx] = false
      })
    },
    goBack() {
      const prevUrl = window.history.state?.back || '';
      if (prevUrl.includes('/markPapers/upload')) {
        this.$router.go(-2);
      } else {
        this.$router.back();
      }
    },
    onStatsDownloadDialogSubmit(formData) {
      let form = {
        fontSize: formData.fontSize,
        segments: formData.segments,
        remoteFileProps: {
          save: formData.save2Remote
        },
        printerProps: {
          print: !!formData.printerProps.printer
        },
        isSaveInteger: formData.isSaveInteger,
        scorePointTypeShowAverageScore: formData.scorePointTypeShowAverageScore,
      }
      this.localFileTableData[0].args = form;
    },
    async onStatsDownloadSubmit(options) {
      if (!this.isExportBatch) {
        let form = {
          taskIds: this.tasks.map(task => task.id),
        }
        Object.assign(form, options);
        Object.assign(form, this.localFileTableData[0].args);
        let loadingMessage = this.$message({
          message: "正在生成统计结果，请勿离开本页面！",
          icon: "Loading",
          type: "warning",
          duration: 0,
        })
        if (form?.segments) {
          form.segments = JSON.stringify(form.segments);
        }
        let fileName = `${this.file?.name + " " || ""}统计结果【请在空白纸打印】.pdf`;
        if (form?.remoteFileProps?.save) {
          form.remoteFileProps.saveFileName = fileName;
        }
        this.$axios.post("/api/docCorrectTask/download/stats", form).then(res => {
          if (this.dontNeedSaveFile.statisticalResults) {
            this.dontNeedSaveFile.statisticalResults = false;
            this.$message.success("保存成功")
            return;
          } else {
            return this.downloadFile(res.data.fileUrl, fileName);
          }
        }).finally(() => {
          loadingMessage.close()
        })
      } else {
        // 批量导出
        for (let i = 0; i < this.localFileDetails.length; i++) {
          let form = {
            taskIds: this.exportBatchFileDetails[this.localFileDetails[i].fileId].taskIds
          }
          Object.assign(form, options);
          Object.assign(form, this.localFileTableData[0].args);
          if (form?.segments) {
            form.segments = JSON.stringify(form.segments);
          }
          let fileName = `${this.localFileDetails[i]?.name + " " || ""}统计结果【请在空白纸打印】.pdf`
          if (form?.remoteFileProps?.save) {
            form.remoteFileProps.saveFileName = fileName;
          }
          form = JSON.parse(JSON.stringify(form));
          if (form?.remoteFileProps?.save && this.localFileDetails[i].folderName) {
            form.remoteFileProps.path = `${form.remoteFileProps.path}/${this.localFileDetails[i].folderName}`;
          }
          const response = await this.$axios.post("/api/docCorrectTask/download/stats", form);
          if (this.dontNeedSaveFile.statisticalResults) {
            this.save2RemoteSuccessCnt++;
            this.$message.success(`保存 ${fileName} 成功`)
          } else {
            this.batchWaitingDownLoadFileList.stats.push({
              url: response.data.fileUrl,
              filename: fileName
            })
          }
        }
        if (this.dontNeedSaveFile.statisticalResults) {
          this.dontNeedSaveFile.statisticalResults = false;
        }
      }

    },
    downloadFile(url, name) {
      // 使用fetch获取文件内容
      return fetch(this.$fileserver.fileurl(url))
        .then(response => response.blob())
        .then(blob => {
          // 如果需要下载，可以使用前面提到的下载代码
          const a = document.createElement("a");
          a.style.display = "none";
          a.href = URL.createObjectURL(blob);
          a.download = name;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(a.href);
        })
        .catch(error => {
          console.error('发生错误:', error);
        });
    },
    onDownloadDialogSubmit(formData) {
      if (formData.isPreview) {
        this.localFileTableData[1].args = formData;
      } else {
        this.localFileTableData[2].args = formData;
      }
    },
    async onDownloadSubmit(options) {
      if (!this.isExportBatch) {
        let form = {
          isPreview: true,
          scoreMerge: true,
          showQsScore: true,
          onlyShowWrongQsScore: false,
          taskIds: this.tasks.map(task => task.id)
        }
        Object.assign(form, options);
        if (form.isPreview) {
          Object.assign(form, this.localFileTableData[1].args);
        } else {
          Object.assign(form, this.localFileTableData[2].args);
        }

        // 根据文件类型显示不同的弹窗消息
        let messageText = "";
        if (form.isPreview) {
          messageText = "正在生成批改含原卷，请勿离开本页面！";
        } else {
          messageText = "正在生成批改不含原卷，请勿离开本页面！";
        }

        let loadingMessage = this.$message({
          message: messageText,
          icon: "Loading",
          type: "warning",
          duration: 0,
        })
        let fileName = `${this.file?.name + " " || ""}_${this.allTaskData[this.tasks[0].id].config.docType || '8k'}_p${this.allTaskData[this.tasks[0].id].records.length * this.tasks.length}_${form.isPreview ? '含原卷【请在空白纸打印】' : '不含原卷【请放入原卷后打印】'}.pdf`;
        if (form?.remoteFileProps?.save) {
          form.remoteFileProps.saveFileName = fileName;
        }
        this.$axios.post("/api/docCorrectTask/download/reviewed", form).then(res => {
          if (options && options.isPreview && this.dontNeedSaveFile.correctionIncludeTheOriginalPaper) {
            this.dontNeedSaveFile.correctionIncludeTheOriginalPaper = false;
            this.$message.success("保存成功")
            return;
          } else if (options && !options.isPreview && this.dontNeedSaveFile.correctionDoesNotIncludeTheOriginalPaper) {
            this.dontNeedSaveFile.correctionDoesNotIncludeTheOriginalPaper = false;
            this.$message.success("保存成功")
            return;
          }
          return this.downloadFile(res.data.fileUrl, fileName);
        }).finally(() => {
          loadingMessage.close()
        })
      } else {
        for (let i = 0; i < this.localFileDetails.length; i++) {
          let form = {
            isPreview: true,
            scoreMerge: true,
            showQsScore: true,
            onlyShowWrongQsScore: false,
            taskIds: this.exportBatchFileDetails[this.localFileDetails[i].fileId].taskIds
          }
          Object.assign(form, options);
          if (form.isPreview) {
            Object.assign(form, this.localFileTableData[1].args);
          } else {
            Object.assign(form, this.localFileTableData[2].args);
          }
          let fileName = `${this.localFileDetails[i]?.name + " " || ""}_${this.exportBatchFileDetails[this.localFileDetails[i].fileId]?.extraParam}_${form.isPreview ? '含原卷【请在空白纸打印】' : '不含原卷【请放入原卷后打印】'}.pdf`
          if (form?.remoteFileProps?.save) {
            form.remoteFileProps.saveFileName = fileName;
          }
          form = JSON.parse(JSON.stringify(form));
          if (form?.remoteFileProps?.save && this.localFileDetails[i].folderName) {
            form.remoteFileProps.path = `${form.remoteFileProps.path}/${this.localFileDetails[i].folderName}`;
          }
          const res = await this.$axios.post("/api/docCorrectTask/download/reviewed", form);
          if (options && options.isPreview && this.dontNeedSaveFile.correctionIncludeTheOriginalPaper) {
            this.save2RemoteSuccessCnt++;
            this.$message.success(`保存 ${fileName} 成功`)
          } else if (options && !options.isPreview && this.dontNeedSaveFile.correctionDoesNotIncludeTheOriginalPaper) {
            this.save2RemoteSuccessCnt++;
            this.$message.success(`保存 ${fileName} 成功`)
          }
          this.batchWaitingDownLoadFileList[form.isPreview ? 'reviewWithOrig' : 'reviewWithoutOrig'].push({
            url: res.data.fileUrl,
            filename: fileName
          })
        }

        if (this.dontNeedSaveFile.correctionIncludeTheOriginalPaper) {
          this.dontNeedSaveFile.correctionIncludeTheOriginalPaper = false;
        } else if (this.dontNeedSaveFile.correctionDoesNotIncludeTheOriginalPaper) {
          this.dontNeedSaveFile.correctionDoesNotIncludeTheOriginalPaper = false;
        }
      }

    },
    loadFile(showLoading = false) {
      if (!this.fileId || this.fileId === 0 || this.fileId === '0') {
        return
      }
      this.taskLoading = true;
      this.$axios.get(`/api/docCorrectFile/getDeep?id=${this.fileId}`).then(res => {
        // this.file = res.data;
        // this.useAimodel = res.data.modelValue
        this.allTaskData = res.data;
        const source = ref(0);
        const transitionValue = useTransition(source, {
          duration: 1500,
        });
        source.value = res.data.rpm ?? 0;
        this.rpm = computed(() => Math.round(transitionValue.value));
        delete this.allTaskData.rpm;
        Object.keys(this.allTaskData).forEach(key => {
          if ('config' in this.allTaskData[key] && 'config' in this.allTaskData[key].config && !('configObj' in this.allTaskData[key].config)) {
            this.allTaskData[key].config.configObj = JSON.parse(this.allTaskData[key].config.config)
          }
          if ('config' in this.allTaskData[key] && 'areas' in this.allTaskData[key].config && !('areasObj' in this.allTaskData[key].config)) {
            this.allTaskData[key].config.areasObj = JSON.parse(this.allTaskData[key].config.areas)
          }
        });
        this.file = res.data.fileDetail;
        this.useAimodel = this.file.modelValue;
        this.initTasks(res.data.tasks);
        this.initScoreRanges();
        this.refreshOffset();
        // 处理pageSettings参数
        this.refreshPageSettings();
        this.tasks.forEach(task => {
          this.loadCropperDetail(res.data[task.id].records, res.data[task.id].config, task.id)
        })
        this.taskLoading = false;

        // 不需要马上加载
        setTimeout(() => {
          this.loadStudents();
        }, 2000)

        this.preview();

        // 不需要马上加载,超过10s说明用户确实想看很多图片
        // setTimeout(()=>{
        //   this.convertSinglePdfUrlToBase64();
        // }, 10000)
      })
    },
    refreshOffset() {
      const tasks = this.allTaskData.tasks;
      tasks.forEach(task => {
        if ('offsetX' in task && task.offsetX !== undefined && 'offsetY' in task && task.offsetY !== undefined) {
          this.setXYOffsetData(task.id, { xOffset: task.offsetX, yOffset: task.offsetY });
        }
      })
    },
    refreshPageSettings() {
      const tasks = this.allTaskData.tasks;
      tasks.forEach(task => {
        const taskData = this.allTaskData[task.id];
        const taskId = task.id;

        // 默认值
        let markZoom = 80;
        let fontSize = 80;
        let opacity = 0.6;

        // 尝试从pageSettings中读取参数
        if (taskData && taskData.config && taskData.config.pageSettings) {
          try {
            const pageSettings = JSON.parse(taskData.config.pageSettings);

            // 设置markZoom参数
            if (pageSettings.markZoom !== undefined) {
              markZoom = parseInt(pageSettings.markZoom) || 80;
            }

            // 设置fontSize参数
            if (pageSettings.fontSize !== undefined) {
              fontSize = parseInt(pageSettings.fontSize) || 80;
            }

            // 设置opacity参数
            if (pageSettings.opacity !== undefined) {
              opacity = parseFloat(pageSettings.opacity) || 0.6;
            }
          } catch (error) {
            console.error(`解析任务 ${task.id} 的pageSettings失败:`, error);
            // 解析失败时使用默认值，不需要额外处理
          }
        }

        // 无论是否读取到pageSettings，都设置参数到store中
        store.setMarkZoomDataById(taskId, markZoom);
        store.setFontSizeDataById(taskId, fontSize);
        store.setOpacityDataById(taskId, opacity);
      });
    },
    bathAllWrongOrRight(data, taskIndex) {
      let { recordIndex, rightOrWrong } = data;
      this.$confirm(`是否确认将这份试卷 一键全${rightOrWrong ? '对' : '错'}`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(res => {
        let reviewedObj = this.allTaskData[this.tasks[taskIndex].id].records[recordIndex].reviewedObj;
        let config = this.allTaskData[this.tasks[taskIndex].id].config;
        let configAreas = JSON.parse(config.areas);
        let hasChange = false;
        reviewedObj.forEach((area, areaIndex) => {
          let areaReviewed = area.reviewed;
          areaReviewed.forEach((qs, qsIndex) => {
            if (qs.isScorePoint === 2) {
              if (!('isCorrectOriginal' in qs)) {
                qs.isCorrectOriginal = qs.isCorrect;
                qs.scoredOriginal = qs.scored;
              }
              let fullMarkers = configAreas[areaIndex].questions[qsIndex].score;
              qs.isCorrect = rightOrWrong ? 'Y' : 'N';
              qs.scored = rightOrWrong ? fullMarkers : 0;
              if (qs.scored !== qs.scoredOriginal) {
                hasChange = true;
              }
            } else {
              if (!('isCorrectOriginal' in qs)) {
                qs.isCorrectOriginal = qs.isCorrect;
              }
              qs.isCorrect = rightOrWrong ? 'Y' : 'N';
              if (qs.isCorrect !== qs.isCorrectOriginal) {
                hasChange = true;
              }
            }
          })
        })
        let param = {
          id: this.allTaskData[this.tasks[taskIndex].id].records[recordIndex].id,
          hasChange: hasChange ? 1 : 0,
          reviewed: JSON.stringify(reviewedObj)
        }
        this.$axios.post("/api/docCorrectRecord/update", param).then((res) => {
          this.refresh();
          this.$message.success("更改成功");
        })
      })

    },
    async bacthEditStuNo(i = 0) {
      let start = i;
      this.$prompt('请输入起始学号，后面的会以此加1', '修改学号', {
        inputValue: '',
        confirmButtonText: '修改',
        cancelButtonText: '取消',
      }).then(async ({ value }) => {
        let startStuNo = value;
        let recordIds = this.allTaskData[this.tasks[0].id].records.map(record => record.id);
        for (; i < recordIds.length; i++) {
          const studentNumber = String(Number(startStuNo) + (i - start)).padStart(startStuNo.length, '0');
          const param = {
            id: recordIds[i],
            studentNumber: studentNumber
          }
          let messageInstance = this.$message.warning(`正在更新第${i + 1}/${recordIds.length - start}个`);
          await this.$axios.post("/api/docCorrectRecord/update", param);
          messageInstance.close();
        }
        this.refresh();
      }).catch((e) => {
        // 用户点击取消时的处理逻辑
        this.$message.info("已取消修改");
      });
    },
    exportJson(task) {
      this.$axios.post("/api/docCorrectRecord/page", {
        taskId: task.id,
        page: {
          searchCount: false,
          pageSize: -1,
        }
      }).then(res => {
        const records = res.data.records
        const jsonData = records.map(record => {
          return {
            docurl: record.docurl,
            docname: record.docname,
            status: record.status,
            showQsScore: record.showQsScore,
            reviewedObj: record.reviewedObj
          }
        })
        const json = JSON.stringify(jsonData, null, 2)
        const blob = new Blob([json], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${task.name}_批改数据.json`
        a.click()
        URL.revokeObjectURL(url)
      })
    },
    async doNameCheckAll() {
      const students = this.students;
      if (!students) {
        this.$message.warning('请配置试卷的班级信息');
        this.openClassChangeDialog()
        return;
      }
      let updatedCount = 0;
      const allStudentRecords = this.allTaskData[this.allTaskData.tasks[0].id].records;
      // 遍历所有record子组件
      for (const record of allStudentRecords) {
        const student = students.find(s => String(s.studentId) === String(record.studentNumber));
        if (student && student.nickname !== record.identify) {
          record.identify = student.nickname;
          // 保存到后端，等待完成
          await this.$axios.post('/api/docCorrectRecord/update', {
            id: record.id,
            identify: student.nickname,
            studentNumber: record.studentNumber
          });
          updatedCount++;
        }
      }

      if (updatedCount) {
        this.$message.success(`批量校对完成，更新了${updatedCount}个姓名`);
        this.refresh();
      } else {
        this.$message.info('没有可校对的姓名');
      }

      // 导出并触发浏览器下载
      loading.close();

      XLSX.writeFile(workbook, `${this.file.name}_样式四.xlsx`);
    },
    exportExcelType3(e) {
      const { url } = e;
      const loading = this.$message({
        message: '正在导出Excel文件，请稍后...',
        duration: 0,
        icon: 'loading',
        type: 'warning'
      })
      this.downloadFile(url, `${this.file.name}_多班级统计结果_美化版.xlsx`);
      loading.close();
    },
    exportExcelType4(e) {
      console.log('e', e)
      const { sheetData, sheetNames, isSortByStuNo, url, realClassNum } = e;
      const loading = this.$message({
        message: '正在导出Excel文件，请稍后...',
        duration: 0,
        icon: 'loading',
        type: 'warning'
      });
      // 确保realClassNum是一个数字，如果是null或undefined则默认为0
      const safeRealClassNum = realClassNum ?? 0;
      // 将realClassNum添加到URL参数中
      const urlWithParams = url + (url.includes('?') ? '&' : '?') + `realClassNum=${safeRealClassNum}`;
      this.downloadFile(urlWithParams, `${this.file.name}_样式四_美化版.xlsx`);
      loading.close();
    },
    exportExcelType2(e) {
      console.log('exportExcelType2', e);
      const loading = this.$message({
        message: '正在导出Excel文件，请稍后...',
        duration: 0,
        icon: 'loading',
        type: 'warning'
      })
      const tasks = this.tasks.map(task => this.allTaskData[task.id])
      const { headers, rows } = createTasksExcelDataType2(tasks, e.ranges, e.students, e.filterEnabled, e.isSortByStuNo)
      // 创建一个新的工作簿
      const workbook = XLSX.utils.book_new();
      // 将数据转换为worksheet
      const worksheet = XLSX.utils.json_to_sheet(rows);
      // 将worksheet添加到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
      XLSX.utils.sheet_add_aoa(worksheet, [[...headers]], { origin: "A1" });
      // 导出并触发浏览器下载
      loading.close()

      XLSX.writeFile(workbook, `${this.file.name}_批改结果.xlsx`);
    },
    exportExcel(isSortByStuNo) {
      const loading = this.$message({
        message: '正在导出Excel文件，请稍后...',
        duration: 0,
        icon: 'loading',
        type: 'warning'
      })
      const tasks = this.tasks.map(task => this.allTaskData[task.id])
      const { headers, rows } = createTasksExcelData(tasks, isSortByStuNo)
      // 创建一个新的工作簿
      const workbook = XLSX.utils.book_new();
      // 将数据转换为worksheet
      const worksheet = XLSX.utils.json_to_sheet(rows);
      // 将worksheet添加到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
      XLSX.utils.sheet_add_aoa(worksheet, [headers], { origin: "A1" });
      // 导出并触发浏览器下载
      loading.close()

      XLSX.writeFile(workbook, this.file.name + '_批改结果.xlsx');
    },
    async doIdCheckAll() {
      const students = this.students;
      if (!students) {
        this.$message.warning('请配置试卷的班级信息');
        this.openClassChangeDialog()
        return;
      }
      let updatedCount = 0;
      const allStudentRecords = this.allTaskData[this.allTaskData.tasks[0].id].records;
      // 遍历所有record子组件
      for (const record of allStudentRecords) {
        const student = students.find(s => s.nickname === record.identify);
        if (student && String(student.studentId) !== String(record.studentNumber)) {
          record.studentNumber = String(student.studentId);
          // 保存到后端，等待完成
          await this.$axios.post('/api/docCorrectRecord/update', {
            id: record.id,
            identify: record.identify,
            studentNumber: String(student.studentId)
          });
          updatedCount++;
        }
      }

      if (updatedCount) {
        this.$message.success(`批量校对完成，更新了${updatedCount}个学号`);
        this.refresh();
      } else {
        this.$message.info('没有可校对的学号');
      }
    },
    exportExcelType5(e) {
      const { sheetData, sheetNames, isSortByStuNo, url, realClassNum } = e;
      const loading = this.$message({
        message: '正在导出Excel文件，请稍后...',
        duration: 0,
        icon: 'loading',
        type: 'warning'
      });
      // 确保realClassNum是一个数字，如果是null或undefined则默认为0
      const safeRealClassNum = realClassNum ?? 0;
      // 将realClassNum添加到URL参数中
      const urlWithParams = url + (url.includes('?') ? '&' : '?') + `realClassNum=${safeRealClassNum}`;
      this.downloadFile(urlWithParams, `${this.file.name}_样式五.xlsx`);
      loading.close();
    },
    exportExcelType6(e) {
      const { sheetData, sheetNames, isSortByStuNo, url, realClassNum } = e;
      const loading = this.$message({
        message: '正在导出Excel文件，请稍后...',
        duration: 0,
        icon: 'loading',
        type: 'warning'
      });
      // 确保realClassNum是一个数字，如果是null或undefined则默认为0
      const safeRealClassNum = realClassNum ?? 0;
      // 将realClassNum添加到URL参数中
      const urlWithParams = url + (url.includes('?') ? '&' : '?') + `realClassNum=${safeRealClassNum}`;
      this.downloadFile(urlWithParams, `${this.file.name}_样式六.xlsx`);
      loading.close();
    },
    refreshCropper() {
      // 弹窗保存后强制刷新 cropper 组件，确保 markZoom/fontSize/opacity 变化立即生效
      if (this.$refs.cropper && this.$refs.cropper.scheduleRender) {
        this.$refs.cropper.scheduleRender();
      } else if (this.$refs.cropper && this.$refs.cropper.$forceUpdate) {
        this.$refs.cropper.$forceUpdate();
      }
    },
    downloadTaskReviewed(task) {
      this.currentDownloadTask = task;
      // 获取区域列表
      const config = this.allTaskData[task.id]?.config;
      this.currentDownloadAreas = (config?.areasObj || []).map((area, idx) => ({
        areaIdx: idx,
        name: area.name || area.label || `区域${idx + 1}`
      }));
      this.selectedAreas = [];
      this.downloadModifiedOnly = false;
      this.downloadAreaDialogVisible = true;
    },
    async handleBatchDownload() {
      const taskId = this.currentDownloadTask.id;
      const records = this.allTaskData[taskId]?.records || [];
      const areaIdxs = this.selectedAreas;
      const imgSet = new Set();
      
      records.forEach(record => {
        areaIdxs.forEach(idx => {
          const area = record.reviewedObj?.[idx];
          if (area) {
            // 如果选择仅下载已修改题目，需要检查该区域是否有已修改的题目
            if (this.downloadModifiedOnly) {
              const hasModifiedQuestions = (area.reviewed || []).some(q => q.hasChange === 1);
              if (hasModifiedQuestions) {
                // 只添加已修改题目的图片
                if (area.areaImg) imgSet.add(area.areaImg);
                (area.reviewed || []).forEach(q => {
                  if (q.hasChange === 1 && q.areaImg) {
                    imgSet.add(q.areaImg);
                  }
                });
              }
            } else {
              // 下载全部题目
              if (area.areaImg) imgSet.add(area.areaImg);
              (area.reviewed || []).forEach(q => {
                if (q.areaImg) imgSet.add(q.areaImg);
              });
            }
          }
        });
      });

      this.downloading = true;
      this.downloadProgress = 0;
      this.downloadTotal = imgSet.size;
      this.downloadStatus = `正在下载图片 0/${this.downloadTotal} ...`;

      const zip = new JSZip();
      let downloadedCount = 0;
      let finished = 0;
      for (const imgUrl of imgSet) {
        const fileName = imgUrl.split('/').pop();
        const realUrl = this.$fileserver.fileurl(imgUrl);
        try {
          const response = await fetch(realUrl, { mode: 'cors' });
          const blob = await response.blob();
          zip.file(fileName, blob);
          downloadedCount++;
        } catch (e) {
          console.error('下载失败:', realUrl, e);
        }
        finished++;
        this.downloadProgress = finished;
        this.downloadStatus = `正在下载图片 ${finished}/${this.downloadTotal} ...`;
      }
      // 打包
      this.downloadStatus = '正在打包压缩文件...';
      zip.generateAsync({ type: 'blob' }).then((content) => {
        this.downloading = false;
        const a = document.createElement('a');
        a.href = URL.createObjectURL(content);
        a.download = `图片批量下载_${downloadedCount}张.zip`;
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(a.href);
        this.$message.success(`图片批量下载完成，已下载${downloadedCount}张`);
        this.downloadAreaDialogVisible = false;
      });
    },
  },
}
</script>

<style lang="scss" scoped>
#wrongIcon {
  width: 24px;
  height: 24px;
  fill: yellow;
}


.el-table {
  table-layout: fixed;
}

:deep(.el-page-header__header) {
  width: 100% !important;
}

:deep(.main-wrapper) {
  height: 100vh;
  /* 整个页面高度 */
  display: flex;
  flex-direction: column;
  /* 垂直布局：Header + 内容区 */
}


.main-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: center;
  height: 100%;
  max-width: 100%;

  .step {
    width: 1069.83px;
    height: 42px;
    flex-shrink: 0;
    margin-bottom: 10px;
  }

  .header-bar {
    width: 100%;
    display: flex;
    margin-bottom: 20px;
    justify-content: space-between;
    align-items: center;
    height: 48px;
    flex-shrink: 0;

    .icon {
      width: 28.28px;
      height: 22.89px;
    }

    .text {
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      letter-spacing: 0;
      margin: 0 10px
    }

    .filter-item {
      margin-right: 10px;
      width: 200px;
    }

    .right {
      margin-left: auto !important;
    }

    .header-action {
      margin-right: 10px;

      :deep(.el-upload) {
        display: inline-block;
      }

      &.header-stats {
        width: 60px;
        text-align: center;
      }

      // 为深色按钮添加鼠标悬浮变浅效果
      &.el-button--primary {
        transition: background-color 0.3s ease, border-color 0.3s ease;

        &:hover {
          background-color: #4a90ff !important;
          border-color: #4a90ff !important;
        }
      }
    }

    .el-button+.el-button {
      margin-left: 0;
    }
  }

  .main-content {
    display: flex;
    height: 100%;
    padding-bottom: 0;
    width: 100%;
    gap: 10px;
    min-width: 900px; // 新增，防止整体被压缩
    overflow-x: auto; // 新增，超出时横向滚动

    .left-content {
      min-width: 0;
      flex-shrink: 0;
    }

    .resize-handle {
      width: 5px;
      height: 100%;
      background-color: #dcdfe6;
      cursor: ew-resize;
      opacity: 0.5;
      flex-shrink: 0;
    }

    .right-content {
      width: 100%;
      min-width: 600px; // 新增，保证右侧内容不会被压缩
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: calc(100% - 10px);
      overflow: auto; // 新增，内容溢出时可滚动

      .change-task {
        position: absolute;
        top: 65px;
        text-decoration: underline;
        cursor: pointer;
      }

      .canvas {
        height: 100%;
        width: 100%;
      }

      .title {
        margin: 0 10px;
        font-size: 15px;
        position: absolute;
        top: 60px;
        right: 20px;
        display: flex;
      }

      .pagination {
        height: 50px;
        margin-top: 5px;
        display: flex;
        align-items: center;
        width: 100%;
        min-width: 350px; // 新增，保证分页条不会被压缩
        flex-shrink: 0; // 新增，始终显示

        .left {
          width: 150px;
          flex-shrink: 0;
        }

        .right {
          flex-grow: 1;
        }

        .icon-with-text {
          display: flex;
          align-items: center;
          gap: 5px;
          white-space: nowrap;
          flex-shrink: 1;
        }

        .tooltip-text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 120px;
          display: inline-block;
          font-size: 14px;
          color: #007bff;
          flex-shrink: 1;
        }
      }
    }
  }
}
</style>