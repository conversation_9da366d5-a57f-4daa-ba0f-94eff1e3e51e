<template>
  <div style="width: 100%;height: 100%">
    <el-form :model="form" ref="formRef" style="width: 100%;height: 100%">
      <div class="container">
        <el-page-header class="header-bar" @back="onBack">
          <template #content>
            <div style="display: flex;width: calc(100vw - 120px);justify-content: space-between">
              <div style="display: flex">
                <el-image src="/icon/16.png" class="icon"></el-image>
                <el-text class="text">开始批改</el-text>
              </div>
              <div style="display: flex;align-items: center">
                <el-switch v-model="cropperShowKuang[paginationOptionsIdx]" style="margin-right: 10px"></el-switch>
                预览框
              </div>
            </div>
          </template>
        </el-page-header>
        <div class="bottom-area">
          <div class="left-content">
            <!--            <el-image src="/icon/paperStep1.svg" class="step"></el-image>-->
            <!--            <el-button-->
            <!--                style="background: transparent;border: none;width: 260px;height: 30px;z-index: 9999;position: fixed;top: 125px;left: 200px;"-->
            <!--                ref="yinDaoRef1" id="yinDaoRef1"></el-button>-->
            <!--            <el-button-->
            <!--                style="background: transparent;border: none;width: 240px;height: 30px;z-index: 9999;position: fixed;top: 125px;left: 450px;"-->
            <!--                ref="yinDaoRef2" id="yinDaoRef2"></el-button>-->
            <!--            <el-button-->
            <!--                style="background: transparent;border: none;width: 240px;height: 30px;z-index: 9999;position: fixed;top: 125px;left: 690px;"-->
            <!--                ref="yinDaoRef3" id="yinDaoRef3"></el-button>-->

            <div class="description" style="">
              <div class="divider"></div>
              <div class="title">选择上传方式：</div>
            </div>
            <div class="bottom">
              <div class="upload-area">
                <el-upload class="upload-container" accept=".pdf"
                           :with-credentials="true"
                           :show-file-list="true"
                           ref="remoteUploaderRef"
                           :file-list="remoteFileList"
                           :auto-upload="false">
                  <div class="upload-item" @click.stop="onRemoteFileSelect">
                    <el-image src="/icon/upload2.svg" class="left-content"></el-image>
                    <div class="right-content">
                      <div class="title">打印机文件上传</div>
                      <div class="subtitle">支持多份试卷批量上传</div>
                    </div>
                  </div>
                </el-upload>

                <el-upload class="upload-container" ref="pdfUploader" accept=".pdf"
                           drag
                           :action="$fileserver.uploadUrl"
                           :with-credentials="true"
                           :show-file-list="true"
                           :auto-upload="false"
                           :file-list="docList"
                           :on-change="onDocChange"
                           :on-success="(...args) => uploadDoc(...args, 'local')"
                           :on-start="onUploadStart"
                           :on-progress="onUploadProgress"
                >
                  <div class="upload-item">
                    <el-image src="/icon/upload1.svg" class="left-content"></el-image>
                    <div class="right-content">
                      <div class="title">文件上传</div>
                      <div class="subtitle">点击此处上传PDF格式文件</div>
                      <div class="speed-text" >传输速度：{{ uploadSpeed ? uploadSpeed : '---' }}</div>
                    </div>
                  </div>

                </el-upload>
              </div>

              <!--              <el-divider style="width: 735px;margin-top: 20px"></el-divider>-->
            </div>
            <div class="description" style="margin-top: 40px">
              <div class="divider"></div>
              <div class="title">文件信息：</div>
            </div>

            <div class="file-area">
              <div style="position: relative;">
                <div class="hint-text">例如：250516-转塘-数学-602</div>
                <el-form-item label="名称：" prop="name" label-width="90">
                  <el-input class="item" v-model="form.name"/>
                </el-form-item>
              </div>
              <div style="position: relative;">
                <div class="model-hint" style="display: flex;justify-content: space-between;width: 210px">
                  <div>推荐第一个</div>
<!--                  <el-link type="primary" @click="$refs.modelRequestSelector.show()">高级设置</el-link>-->
                </div>
                <el-form-item label="批改模型：" prop="code" label-width="90">
                  <el-select class="item" suffix-icon="Search"
                           placeholder="请选择模型"
                           v-model="form.modelValue"
                           :value-key="'label'">
                    <el-option v-for="item in getAimodelOptions" :key="item" :label="item.label" :value="item" style="width: 400px"/>
                  </el-select>
                </el-form-item>
              </div>
              <el-form-item label="标准卷：" prop="code" label-width="90">
                <el-select class="item" suffix-icon="Search" ref="packagesSelect"
                           placeholder="请输入标准卷名称" remote filterable clearable
                           :remote-method="loadPackagesOptions" :loading="packagesLoading" @change="onPackagesSelect"
                           v-model="packagesId">
                  <el-option v-for="item in packagesOptions" :key="item.id" :label="item.name" :value="item.id"
                             style="width: 300px"/>
                </el-select>
              </el-form-item>
              <div style="position: relative;">
                <div class="hint-text">对应循环页数</div>
                <el-form-item label="页数：" prop="pageNum" label-width="90">
                  <el-input-number class="item" :min="1" :controls="false" v-model="form.pageNum"
                                   :value-on-clear="1"/>
                </el-form-item>
              </div>
              <el-form-item label="选择班级：" label-width="90">
                <el-cascader
                    v-model="form.classId"
                    :props="cascaderProps"
                    placeholder="请选择班级"
                ></el-cascader>
              </el-form-item>

              <!--              <el-form-item label="文件大小：" label-width="90">-->
              <!--                <el-input class="item" v-model="fileInformation.size" disabled/>-->
              <!--              </el-form-item>-->
              <el-form-item label="试卷份数：" label-width="90">
                <el-input class="item" v-model="fileInformation.cnt" disabled/>
              </el-form-item>
              <!--              <el-form-item label="预计耗时：" label-width="90">-->
              <!--                <el-input class="item" v-model="fileInformation.correctTime" disabled/>-->
              <!--              </el-form-item>-->

              <div style="display: flex;flex-direction: column">
                <el-form-item v-for="(page, index) in form.pages" :key="index" :label="`第${index + 1}页：`"
                              label-width="90">
                  <el-row>
                    <el-space :size="20">
                      <el-switch inactive-text="解析" v-model="page.isParse"/>
                      <el-switch inactive-text="翻转" v-model="page.isRotation"/>
                      <el-select v-model="page.configId"
                                 filterable remote
                                 style="width: 200px"
                                 :loading="page.configLoading"
                                 :remote-method="(query) => loadConfigs(query, page)"
                                 placeholder="请选择试卷配置">
                        <el-option
                            v-for="item in page.configOptions"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                      </el-select>
                      <el-button v-if="form.pageNum !== 1" type="danger" text icon="Delete"
                                 @click="form.pages.splice(index, 1)"/>
                    </el-space>
                  </el-row>
                  <el-row style="margin-top: 10px">
                    <el-space :size="20">
                      <el-form-item v-if="offsets?.[index]" label="X轴位移大小" prop="xOffset">
                        <el-input-number v-model="offsets[index].xOffset" :step="20" style="width: 120px"/>
                      </el-form-item>
                      <el-form-item v-if="offsets?.[index]" label="Y轴位移大小" prop="yOffset">
                        <el-input-number v-model="offsets[index].yOffset" :step="20" style="width: 120px;"/>
                      </el-form-item>
                    </el-space>

                  </el-row>


                </el-form-item>
              </div>

            </div>

          </div>
          <div class="right-content">
            <cropper ref="cropper1" class="canvas" :id="'temp' + (this.nowCropperPageIdx - 1) % this.form.pageNum"
                     :loading="loadingCropper[0]" :is-empty="!cropperImageList[0].length">
            </cropper>

            <div class="pagination" v-show="cropperImageList[0].length || cropperImageList[1].length">
              <el-select v-model="paginationOptionsIdx" class="left" @change="changePaginationOptionsIdx">
                <el-option
                    v-for="(item,index) in paginationOptions"
                    :key="index"
                    :label="item"
                    :value="index"
                >
                </el-option>
              </el-select>

              <div style="width: 5px;flex-shrink: 1"></div>
              <el-pagination
                  background
                  layout="prev, pager, next"
                  :total="cropperImageList[paginationOptionsIdx].length"
                  v-model:current-page="nowCropperPageIdx"
                  :page-size="1"
                  class="right"
                  @current-change="currentPageChange"
              />
<!--              <div v-if="paginationOptionsIdx === 0">-->
<!--                <el-tooltip class="item" effect="dark" content="勾叉和框的位置整体移动-影响导出和批改" placement="top">-->
<!--                  <span class="icon-with-text">-->
<!--                    <el-icon @click="$refs.XYOffsetDisplacementDialog.show()" color="#007bff" size="20px">-->
<!--                      <Operation/>-->
<!--                    </el-icon>-->
<!--                    <span @click="$refs.XYOffsetDisplacementDialog.show()" class="tooltip-text">勾叉/框位移</span>-->
<!--                  </span>-->
<!--                </el-tooltip>-->
<!--              </div>-->
            </div>
          </div>

        </div>


        <div class="bottom-button-area">
          <el-button class="confirm-button" @click="onSubmit">下一步</el-button>
          <el-button class="cancel-button" @click="onBack">取消</el-button>
        </div>
      </div>
    </el-form>

    <!-- 远程文件列表 -->
    <file-selector ref="remoteFileSelectorRef" type="upload" @file-selected="onRemoteFileSelected"/>
    <!--    <el-tour v-model="needMarkPaperGuide" type="primary" :mask="false" @close="finishTour">-->
    <!--      <el-tour-step target="#yinDaoRef1" title="上传样卷"-->
    <!--                    description="首先需要上传学生试卷，并填写试卷名称"></el-tour-step>-->
    <!--      <el-tour-step target="#yinDaoRef2" title="批改试卷"-->
    <!--                    description="其次需要点击批改试卷，如果同一个文件已经配置过试卷，会自动匹配自动批改"/>-->
    <!--      <el-tour-step target="#yinDaoRef3" title="导出结果"-->
    <!--                    description="最后需要导出批改结果，有三种批改结果：1. 统计结果 2. 批改结果不含原卷（原卷是指您上传文件，如果不含则只有 对错 符号和分数） 3. 批改结果含原卷"/>-->
    <!--    </el-tour>-->
    <img src="/flag.svg" id="flagimg" style="display: none;"/>
    <config-selection-dialog ref="configSelectionDialog"
                             @submit="configSelectionDialogSubmit"></config-selection-dialog>
    <!--    <XYOffsetDisplacementDialog :id="'temp' + (this.nowCropperPageIdx - 1) % this.form.pageNum" :ids="taskIds"-->
    <!--                                ref="XYOffsetDisplacementDialog"></XYOffsetDisplacementDialog>-->

    <ModelRequestSelector ref="modelRequestSelector" @select="modelRequestSelect"></ModelRequestSelector>
  </div>
</template>

<script>
import Cropper from '@/components/cropper/forPreview.vue';
import {ElLoading} from "element-plus";
import FileSelector from "../../common/FileSelector.vue";
import {mapActions, mapState} from "pinia";
import {useUserStore} from "@/store";
import ConfigSelectionDialog from './ConfigSelectionDialog.vue';
import * as pdfjsLib from "pdfjs-dist";
import {drawRect} from "@/utils/imgUtil";
import XYOffsetDisplacementDialog from "@/views/common/XYOffsetDisplacementDialog.vue";
import ModelRequestSelector from './ModelRequestSelector.vue'

const store = useUserStore();
export default {
  components: {
    XYOffsetDisplacementDialog,
    FileSelector,
    Cropper,
    ConfigSelectionDialog,
    ModelRequestSelector
  },
  data() {
    return {
      offsets: [{id: 'temp0', xOffset: 0, yOffset: 0}, {id: 'temp1', xOffset: 0, yOffset: 0}],
      title: '开始批改',
      form: {
        classId: null,
        name: '',
        pageNum: 2,
        packagesConfig: '',
        pages: [
          {isParse: true, isRotation: false, configId: '', configOptions: [], configLoading: false},
          {isParse: true, isRotation: false, configId: '', configOptions: [], configLoading: false},
        ],
        modelValue: store.getAimodelOptions[store.getDefaultCorrectModal],
        jsonobject: false,
        jsonschema: false,
        url: ''
      },
      docList: [],
      action: 'correct',
      codeLoading: false,
      loadingInstance: null,
      fileInformation: {
        size: '',
        cnt: null,
        correctTime: null,
        type: null
      },
      files: null,
      packagesLoading: false,
      packagesOptions: [],
      packageConfigDetail: [],
      useDefaultConfig: false,
      uploadFile: null,
      fileUploading: false,
      remoteFileList: [],
      packagesId: null,
      packagesName: '',
      loadingCropper: [false, false],
      cropperShowKuang: [true, true],
      paginationOptions: ['上传的文件', '匹配到的配置卷'],
      paginationOptionsIdx: 0,
      cropperImageList: [[], []],
      nowCropperPageIdx: 1,
      imagesList: {},
      imgDataUrl: ['', ''],
      uploadSelectConfigPackage: false,
      forceAdjust: false,
      selectedClassId: '',
      cascaderProps: {
        lazy: true,
        lazyLoad: (node, resolve) => {
          const {level, data} = node;
          if (level === 0) {
            this.$axios.get('/api/school/all').then(response => {
              // 假设接口返回结果形如 { code:200, data: [...] }

              if (response.data && response.data.length > 0) {
                const schools = response.data || [];
                const transformed = schools.map(item => ({
                  id: item.schoolId,
                  name: item.schoolName,
                  leaf: false
                }));
                resolve(transformed);
              } else {
                resolve([]);
              }
            }).catch(() => {
              resolve([]);
            });
          } else if (level === 1) {
            this.$axios.get(`/api/class/by-school/${data.id}`).then(response => {
              if (response.data && response.data.length > 0) {
                const classes = response.data || [];
                const transformed = classes.map(item => ({
                  id: item.classId,
                  name: item.className,
                  leaf: true
                }));
                resolve(transformed);
              } else {
                resolve([]);
              }
            }).catch(() => {
              resolve([]);
            });
          }
        },
        // 假设学校和班级的数据格式均包含 id 与 name 字段
        value: 'id',
        label: 'name',
        // 设置 emitPath 为 false，可直接返回最后一项（班级）的 id
        emitPath: false
      },
      jsonobject: false,
      jsonschema: false,
      uploadSpeed: '',
      _lastLoaded: 0,
      _lastTimestamp: 0,
      selectRemoteFileRow: null,
      ftpMessageFileDetail: null,
      ftpMessageFileId: null,
      modelRequestId: null
    }
  },
  computed: {
    modelRequest() {
      return modelRequest
    },
    ...mapState(useUserStore, ['getAimodelOptions']),
    taskIds() {
      let ids = [];
      for (let i = 0; i < this.form.pageNum; i++) {
        ids.push('temp' + i);
      }
      return ids;
    }
  },
  watch: {
    offsets: {
      handler(newVal) {
        newVal.forEach((item) => {
          this.setXYOffsetData(item.id, {
            xOffset: item.xOffset,
            yOffset: item.yOffset
          });
        })
      },
      deep: true
    },
    "cropperShowKuang": {
      handler(val) {
        this.refresh()
      },
      deep: true
    },
    "form.pageNum": {
      handler(val) {
        if (val > this.form.pages.length) {
          for (let i = this.form.pages.length; i < val; i++) {
            this.form.pages.push({
              isParse: true,
              isRotation: false,
              configId: '',
              configOptions: [],
              configLoading: false
            });
          }
        } else {
          this.form.pages = this.form.pages.slice(0, val);
        }
        // 多的删除，少了新增
        this.offsets = this.offsets.slice(0, val);
        for (let i = 0; i < val; i++) {
          if (!this.offsets[i]) {
            this.offsets[i] = {
              id: 'temp' + i,
              xOffset: 0,
              yOffset: 0
            }
          }
        }
      }
    },
    "form.pages.length": {
      handler(val) {
        this.form.pageNum = val;
      }
    },
    'nowCropperPageIdx': {
      handler(val) {
        this.refresh();
      }
    },
    'paginationOptionsIdx': {
      handler(val) {
        this.forceAdjust = true;
        this.refresh();
      }
    },
    'form.modelValue': {
      handler(val) {
        this.form.jsonobject = val?.jsonobject || false;
        this.form.jsonschema = val?.jsonschema || false;
      },
      immediate: true
    }
  },
  mounted() {
    this.$refreshConfig();

    // 清理temp数据
    for (let i = 0; i < 10; i++) {
      this.removeXYOffsetData('temp' + i);
    }
    this.form.name = this.getBaseName();
    this.ftpMessageFileId = this.$route.query?.ftpMessageFileId ?? null;
    const fileDetail = this.$route.query?.ftpMessageDetail ?? null;
    if (fileDetail) {
      const fileDetailJsonString = decodeURIComponent(fileDetail);
      this.ftpMessageFileDetail = JSON.parse(fileDetailJsonString);
      this.initByFtpMessage()
      if ( this.ftpMessageFileDetail?.formFileName) {
        this.form.name = this.getBaseName() + this.ftpMessageFileDetail?.formFileName;
      }
    }
    this.getLatestWithin30Min();
  },
  methods: {
    ...mapActions(useUserStore, ['removeXYOffsetData', 'setXYOffsetData']),
    modelRequestSelect(id) {
      this.modelRequestId = id;
    },
    getLatestWithin30Min() {
      this.$axios.get('/api/docCorrectConfigPackage/latestWithin30Min').then(res => {
        if (res.data) {
          // this.$message.success(`找到最新的标注卷:${res.data.name}`)
          this.packagesOptions = [res.data];
          // this.packagesId = res.data.id;
          this.$refs.packagesSelect.toggleMenu();
          // this.onPackagesSelect(res.data.id)
        }
      });
    },
    initByFtpMessage() {
      const ftpMessageFileDetail = this.ftpMessageFileDetail;
      let obj = {
        name: ftpMessageFileDetail.name,
        percentage: 100,
        status: 'success',
        url: ftpMessageFileDetail.url
      }
      this.onRemoteFileSelected(obj, ftpMessageFileDetail)
    },
    previewFlags() {
      if (!this.packageConfigDetail.length || !this.cropperShowKuang[this.paginationOptionsIdx]) {
        return {
          areas: [],
          answers: []
        }
      }
      // 按照页数判断，配置页面两页 第
      let config = this.packageConfigDetail[(this.nowCropperPageIdx - 1) % this.packageConfigDetail.length];

      let areas = []
      let answers = [];
      let kuangs = [];
      //

      config.areasObj.forEach(area => {
        area.questions.forEach(qs => {
          areas.push(Object.assign({
            color: 'green',
            mark: document.getElementById("flagimg"),
            isScorePoint: qs.isScorePoint,
            text: qs.answer === '无' ? qs.scored : qs.answer,
          }, qs.flagArea))
          answers.push({
            area: qs.flagArea,
            text: qs.answer === '无' ? qs.scored : qs.answer,
          })
        })
      })
      let configObj = config.configObj;
      if (configObj.nameArea && 'x' in configObj.nameArea) {
        kuangs.push({
          area: configObj.nameArea,
          type: 'identify',
          color: '#8A2BE2'
        })
      }
      if (configObj.studentNumberArea && 'x' in configObj.studentNumberArea) {
        kuangs.push({
          area: configObj.studentNumberArea,
          type: 'studentNumber',
          color: '#8A2BE2'
        })
      }
      if (configObj.totalScoreArea && 'x' in configObj.totalScoreArea) {
        kuangs.push({
          area: configObj.totalScoreArea,
          color: '#e8a3a3'
        })
      }
      if (configObj.score && configObj.scoreArea && 'x' in configObj.scoreArea) {
        kuangs.push({
          area: configObj.scoreArea,
          color: '#e8a3a3'
        })
      }

      const configAreas = config.areasObj;
      if (configAreas && configAreas.length > 0) {
        kuangs.push(...configAreas.map(item => {
          return {
            area: item.area,
            color: item.areaType === 4 ? 'red' : ''
          }
        }))
      }

      return {
        areas,
        answers,
        kuangs
      }
    },
    refresh() {
      if (this.cropperImageList[this.paginationOptionsIdx].length === 0) {
        this.imgDataUrl[this.paginationOptionsIdx] = null;
        this.$message.warning("请上传试卷,没有图片预览");
        return;
      }
      this.imgDataUrl[this.paginationOptionsIdx] = this.cropperImageList[this.paginationOptionsIdx][this.nowCropperPageIdx - 1];
      this.convertPdfUrlToBase64(this.cropperImageList[this.paginationOptionsIdx][(this.nowCropperPageIdx - 1) < this.cropperImageList[this.paginationOptionsIdx].length ? (this.nowCropperPageIdx - 1) : 0].pdfUrl,
          this.cropperImageList[this.paginationOptionsIdx][this.nowCropperPageIdx < this.cropperImageList[this.paginationOptionsIdx].length ? this.nowCropperPageIdx : 0]?.pdfUrl);
    },
    ...mapActions(useUserStore, ['setGuideStatus']),
    changePaginationOptionsIdx(e) {

    },
    currentPageChange(e) {

    },
    finishTour() {
      this.setGuideStatus('markPaper', false)
    },
    onBack() {
      this.$router.back()
    },
    async onPackagesSelect(e) {
      let config = this.packagesOptions.find(item => item.id === e);
      this.packagesLoading = true;
      this.form.packagesConfig = config;
      let configIds = JSON.parse(config.config);
      this.packageConfigDetail = []
      this.cropperImageList[1] = []
      for (let i = 0; i < configIds.length; i++) {
        let response = await this.$axios.get(`/api/docCorrectConfig/get?id=${configIds[i]}`);
        if ('areas' in response.data && !('areasObj' in response.data)) {
          response.data.areasObj = JSON.parse(response.data.areas)
        }
        if ('config' in response.data && !('configObj' in response.data)) {
          response.data.configObj = JSON.parse(response.data.config)
        }
        if (!response.data) {
          this.$message.error("获取配置失败");
          return;
        }
        this.packageConfigDetail.push(response.data);
        this.cropperImageList[1].push({
          pdfUrl: response.data.docurl,
          imageUrl: response.data.img
        })
      }
      // 渲染每一页的配置
      let pages = []
      this.packageConfigDetail.forEach((item, index) => {
        pages.push({
          configId: item.id,
          configName: item.name,
          configOptions: [
            {id: item.id, name: item.name}
          ],
          docs: [],
          configLoading: false,
          isParse: true,
          isRotation: false,
          options: []
        })
      });
      this.form.pages = pages;
      this.packagesLoading = false;
      // this.paginationOptionsIdx = 1;
      // if (this.paginationOptionsIdx === 1) {
      //   this.refresh()
      // }
      this.refresh();
    },
    loadPackagesOptions(query, fromDocChange = false) {
      console.log('fromDocChange', fromDocChange)
      if (!query || query === '') return;
      this.packagesLoading = true;
      let form = {
        name: query,
        page: {
          searchCount: false,
          pageSize: -1,
        }
      };
      this.$axios.post(`/api/docCorrectConfigPackage/page`, form).then(res => {
        let records = res.data.records;
        this.packagesOptions = records;
        if (fromDocChange && this.useDefaultConfig && this.packagesOptions.length && this.uploadSelectConfigPackage) {
          // 提示用户选择使用那个配置卷
          this.uploadSelectConfigPackage = false;
          // 改为名字
          if (res.data.records.length === 1) {
            this.packagesId = res.data.records[0].id;
          }
          if (res.data.records.length) {
            this.$refs.packagesSelect.toggleMenu();
          }
          // this.$refs.configSelectionDialog.show(records.slice(0, 10))
        }
      }).finally(() => {
        this.packagesLoading = false;
      });
    },
    configSelectionDialogSubmit(configId) {
      if (!configId) return;
      this.useDefaultConfig = false;
      this.packagesId = configId
      this.onPackagesSelect(configId);
    },
    onClose(id) {
      this.$refs.formRef.resetFields()
      this.docList = []
      this.remoteFileList = []
      this.isShow = false
      this.$emit("finishUpload", id);
    },
    onSubmit(action) {
      this.action = 'submit'
      // 检测重名
      this.$axios.get("/api/docCorrectFile/checkName?name=" + this.form.name).then(res => {
        if (res.code === 200) {
          if (!(this.docList.length !== 0 || this.remoteFileList.length !== 0)) {
            this.$message.error("请上传试卷");
            return;
          }
          if (this.fileUploading) {
            this.$message.warning("请等待试卷上传完成");
            return;
          }
          this.submit()
        }
      });

    },
    async submit() {
      this.loadingInstance = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      let isExecute = false;
      let needCreateConfigPackage = false;
      let pages = this.form.pages.filter(item => item.isParse)
          .map((item, index) => {
            const configObj = item.configOptions?.find(config => config.id === item.configId)
            let docs = [];
            this.files.forEach((file, i) => {
              if (i % this.form.pageNum === index) {
                docs.push(file);
              }
            });
            if (!item.configId) {
              needCreateConfigPackage = true;
            }
            return {
              ...item,
              configId: item.configId,
              configName: configObj?.name,
              docs: docs
            }
          });
      if (needCreateConfigPackage) {
        pages = await this.createConfigPackage(pages);
      }
      let offset = [];
      for (let i = 0; i < this.form.pageNum; i++) {
        let offsetItem = store.getXYOffsetData('temp' + i);
        offset.push({
          offsetX: offsetItem?.xOffset ?? 0,
          offsetY: offsetItem?.yOffset ?? 0
        });
      }
      let createForm = {
        ...this.form,
        modelValue: this.form.modelValue.value,
        pages,
        action: 'correct',
        offset: offset,
        configPackageId: this.packagesId,
        recordSize: Math.ceil(this.cropperImageList[this.paginationOptionsIdx].length / this.form.pageNum),
        remark: this.selectRemoteFileRow?.fileRemark,
        modelRequestId: this.form.modelValue?.modelRequestId
      };
      const timerId = setInterval(() => {
        if (createForm && !isExecute) {
          isExecute = true;
          this.$axios.post("/api/docCorrectFile/create", createForm).then(async res => {
            if (this.selectRemoteFileRow) {
              const row = this.selectRemoteFileRow;
              const param = {
                id: row?.id,
                remark: this.form.name,
                filename: row.name,
                modified: row.modified,
                path: row.path,
                size: row.size,
                type: 'file',
                fileId: res.data?.id,
                configPackageId: res.data?.configPackageId
              }
              await this.$axios.post(`/api/ftpFilesRemark/${'remark' in row ? 'update' : 'add'}`, param);
            }

            clearInterval(timerId);
            this.$message.success("文件上传成功");
            this.$router.push({path: `/markPapers/stepMarkPapers/${res.data.id}`})
            if (this.loadingInstance) {
              this.loadingInstance.close();
            }
          });
        }
      }, 1000);

    },
    async createConfigPackage(pages) {
      this.loadingInstance = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      let packageIds = []
      // 用的配置试卷里面 创建配置的方法。默认配置
      let saveConfigDefaultForm = {
        areas: "[]",
        config: "{\"score\":true,\"scoreFormat\":1,\"scoreColor\":\"red\",\"scoreFontSize\":10,\"fontSize\":10,\"flagSize\":20,\"flagColor\":\"red\",\"errorFlagColor\":\"red\",\"errorFlagSize\":20,\"errorFlag\":\"x\",\"correctFlag\":\"a\",\"nameArea\":null,\"scoreArea\":null,\"additionalName\":\"附加\",\"prompt\":null,\"qsOcrPrompt\":null,\"scoreTypes\":[\"总分\"]}",
        docType: store.getDefaultDocType,
        docurl: "",
        img: "",
        name: ""
      };
      for (let pageNum = 0; pageNum < pages.length; pageNum++) {
        if (!pages[pageNum].configId && pages[pageNum].isParse) {
          let filePath = pages[pageNum].docs[0].url;
          let filename = filePath.split('/').pop();
          const pdf2ImgResponse = await this.$axios.get("/api/file/pdf2Img?filename=" + filename + '&pageNum=1');
          if (!pdf2ImgResponse.data) {
            this.$message.error("文件转换失败");
            return;
          }
          saveConfigDefaultForm.name = this.form.name + "_页" + (pageNum+1);
          saveConfigDefaultForm.docurl = pdf2ImgResponse.data.docpath;
          saveConfigDefaultForm.img = pdf2ImgResponse.data.url;
          const saveConfigResponse = await this.$axios.post("/api/docCorrectConfig/update", saveConfigDefaultForm);
          if (!saveConfigResponse.data) {
            this.$message.error("配置保存失败");
            return;
          }
          pages[pageNum].configId = saveConfigResponse.data.id;
          pages[pageNum].isParse = false;
          pages[pageNum].configName = saveConfigDefaultForm.name;
          pages[pageNum].configOptions = [{
            id: saveConfigResponse.data.id,
            name: saveConfigDefaultForm.name
          }];
          packageIds.push(saveConfigResponse.data.id);
        } else if (pages[pageNum].configId) {
          packageIds.push(pages[pageNum].configId);
        }
      }
      const now = new Date();
      const pad = n => String(n).padStart(2, '0');
      const timestamp =
          now.getFullYear().toString() + '-' +
          pad(now.getMonth() + 1) + '-' +
          pad(now.getDate()) + '-' +
          pad(now.getHours()) + ':' +
          pad(now.getMinutes()) + ':' +
          pad(now.getSeconds());
      const createForm = {
        name: `${this.form.name}-${timestamp}`,
        config: JSON.stringify(packageIds),
      };

      const savePackageConfigResponse = await this.$axios.post("/api/docCorrectConfigPackage/add", createForm);
      if (!savePackageConfigResponse.data) {
        this.$message.error("配置保存失败");
        return;
      }
      this.packagesId = savePackageConfigResponse.data.id;
      this.form.packagesConfig = JSON.stringify(savePackageConfigResponse.data);

      return pages;
    },
    async uploadDoc(response, file, data, type) {
      this.form.url = response.data.url;

      const multiPdfRes = await this.$axios.post('/api/file/upload/multiPdf', {
        config: JSON.stringify({
          ...this.getParseConfig(),
          file: response.data.url
        })
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })

      let files = multiPdfRes.data
      if (!files || files.length === 0) {
        this.$message.error("文件上传失败!");
        return;
      }
      let pdfList = [];
      if (multiPdfRes.data.length) {
        multiPdfRes.data[0].forEach((it) => {
          pdfList.push({
            pdfUrl: it.url,
            imageUrl: ''
          })
        })
      }
      this.paginationOptionsIdx = 0;
      this.cropperImageList[0] = pdfList;
      this.convertPdfUrlToBase64(pdfList[0].pdfUrl, pdfList[1]?.pdfUrl);
      this.fileInformation.type = type;
      // 计算页数和预计时间
      const count = multiPdfRes.data.reduce((total, innerArray) => total + innerArray.length, 0);
      this.fileInformation.cnt = count;
      this.fileInformation.correctTime = Math.floor(count / 10) || 1 + '分钟';

      this.files = files[0];
      this.fileUploading = false;

      this.uploadSpeed = '';
    },
    onDocChange(uploadFile) {
      if (uploadFile === this.uploadFile) {
        return;
      }
      this.uploadFile = uploadFile;
      this.fileInformation.size = (uploadFile.size / 1024 / 1024).toFixed(2) + 'MB';
      let status = uploadFile.status
      if (status === 'ready') {
        this.docList = [uploadFile]

        this.form.name = this.getBaseName() + uploadFile.name?.split('.')[0] || '';
      }
      this.packagesName = uploadFile.name?.split('.')[0] + "";
      this.useDefaultConfig = true;
      this.uploadSelectConfigPackage = true;
      this.loadPackagesOptions(this.packagesName, true);
      this.fileUploading = true;
      this.uploadSpeed = '';
      this.$refs.pdfUploader.submit();
    },
    onUploadStart(file) {
      this._lastTimestamp = Date.now();
      this._lastLoaded = 0;
      this.uploadSpeed = '计算中...';
    },
    /**
     * onUploadProgress 方法签名：
     * 第一个参数是原生 ProgressEvent（包含 loaded / total），
     * 第二个参数才是元素本身封装的 UploadFile 对象。
     */
    onUploadProgress(evt, file, fileList) {
      const now = Date.now();
      const elapsedMs = now - this._lastTimestamp;
      if (elapsedMs <= 0) return;

      // 已上传的总字节数
      const loaded = evt.loaded;
      const deltaLoaded = loaded - this._lastLoaded;
      const speedBytesPerSec = deltaLoaded / (elapsedMs / 1000);

      this._lastLoaded = loaded;
      this._lastTimestamp = now;
      // 格式化为 B/s、KB/s、MB/s
      let speedText = "";
      if (speedBytesPerSec < 1024) {
        speedText = `${speedBytesPerSec.toFixed(0)} B/s`;
      } else if (speedBytesPerSec < 1024 * 1024) {
        speedText = `${(speedBytesPerSec / 1024).toFixed(2)} KB/s`;
      } else {
        speedText = `${(speedBytesPerSec / (1024 * 1024)).toFixed(2)} MB/s`;
      }

      this.uploadSpeed = speedText;
    },
    getBaseName(fileName) {
      const now = new Date();
      const year = now.getFullYear().toString().slice(-2);
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const datePrefix = `${year}${month}${day}-`;
      return datePrefix;
    },
    getParseConfig() {
      let form = JSON.parse(JSON.stringify(this.form));
      form.pageNum = 1;
      return {
        config: JSON.stringify(form)
      };
    },
    loadConfigs(query, page) {
      if (query === '') {
        page.options = []
        return
      }

      page.configLoading = true
      this.$axios.post("/api/docCorrectConfig/page", {
        name: query,
        page: {
          pageSize: -1,
          pageNumber: 1
        }
      }).then(res => {
        page.configOptions = res.data.records
      }).finally(() => {
        page.configLoading = false
      })
    },
    onRemoteFileSelect() {
      this.$refs.remoteFileSelectorRef.show()
    },
    onRemoteFileSelected(uploadFile, row) {
      console.log('uploadFile', uploadFile, row)
      this.selectRemoteFileRow = row;
      let filename = uploadFile.name.split('.')[0];
      this.form.name = this.getBaseName() + filename;
      this.remoteFileList = [uploadFile]
      const loading = this.$message.warning({
        message: '解析文件中...',
        duration: 0,
        icon: 'loading'
      })
      this.form.url = uploadFile.url
      this.$axios.post('/api/file/upload/multiPdf', {
        config: JSON.stringify({
          ...this.getParseConfig(),
          file: uploadFile.url
        })
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }).then(multiPdfRes => {
        loading.close()
        this.$message.success('文件解析成功')

        let files = multiPdfRes.data
        if (!files || files.length === 0) {
          this.$message.error("文件上传失败!");
          return;
        }
        let pdfList = [];
        if (multiPdfRes.data.length) {
          multiPdfRes.data[0].forEach((it) => {
            pdfList.push({
              pdfUrl: it.url,
              imageUrl: ''
            })
          })
        }
        this.paginationOptionsIdx = 0;
        this.cropperImageList[0] = pdfList;
        this.convertPdfUrlToBase64(pdfList[0].pdfUrl, pdfList[1]?.pdfUrl);
        this.fileInformation.type = 'remote';
        // 计算页数和预计时间
        const count = multiPdfRes.data.reduce((total, innerArray) => total + innerArray.length, 0);
        this.fileInformation.cnt = count;
        this.fileInformation.correctTime = Math.floor(count / 10) || 1 + '分钟';

        this.files = files[0];
        this.fileUploading = false;
      }).finally(() => {
        loading.close()
      })
    },
    async convertPdfUrlToBase64(pdfUrl, nextDocUrl) {
      this.loadingCropper[this.paginationOptionsIdx] = true;
      try {
        // 通过 fetch 下载 PDF 文件
        if (pdfUrl in this.imagesList) {
          this.imgDataUrl[this.paginationOptionsIdx] = this.imagesList[pdfUrl];
          this.loadingCropper[this.paginationOptionsIdx] = false;
          this.preview();
          this.loadNextImage(nextDocUrl)
        } else {
          const response = await fetch(this.$fileserver.fileurl(pdfUrl));
          // 异步提前缓存下一个
          this.loadNextImage(nextDocUrl)
          if (!response.ok) {
            throw new Error('Failed to fetch PDF');
          }
          const blob = await response.blob();
          const pdfData = await blob.arrayBuffer();
          const pdfDoc = await pdfjsLib.getDocument(pdfData).promise;

          // 获取 PDF 的总页数
          const totalPages = pdfDoc.numPages;
          const images = [];

          // 设置目标 DPI（300 DPI）
          const targetDPI = 300;

          // 获取每英寸的像素数，假设 PDF 使用标准的 72 DPI
          const scaleFactor = targetDPI / 72;  // PDF 默认分辨率通常是 72 DPI

          // 遍历每一页，将其转换为 Base64 图像
          if (totalPages === 1) {
            const page = await pdfDoc.getPage(1);

            // 创建一个 canvas 元素
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');

            // 获取页面的渲染视口
            const viewport = page.getViewport({scale: scaleFactor});

            // 设置 canvas 大小为页面大小
            canvas.width = viewport.width;
            canvas.height = viewport.height;

            // 将页面渲染到 canvas 上
            await page.render({
              canvasContext: context,
              viewport: viewport,
            }).promise;
            const imgDataUrl = canvas.toDataURL('image/jpeg');
            images.push(imgDataUrl);
            this.loadingCropper[this.paginationOptionsIdx] = false;
            this.imgDataUrl[this.paginationOptionsIdx] = imgDataUrl;
            this.imagesList[pdfUrl] = imgDataUrl;
            this.preview();
          }
        }

      } catch (error) {
        console.error('PDF 转换失败', error);
      }
    },
    preview() {
      const {areas, answers, kuangs} = this.previewFlags();
      let forceAdjust = false;
      if (this.forceAdjust) {
        this.forceAdjust = false;
        forceAdjust = true;
      }

      this.$refs.cropper1.setImg(this.imgDataUrl[this.paginationOptionsIdx], areas, answers, kuangs ?? [], forceAdjust);
    },
    async loadNextImage(nextDocUrl) {
      if (!nextDocUrl) return;
      if (nextDocUrl in this.imagesList) return;
      try {
        const response = await fetch(this.$fileserver.fileurl(nextDocUrl));

        if (!response.ok) {
          throw new Error('Failed to fetch PDF');
        }

        const blob = await response.blob();
        const pdfData = await blob.arrayBuffer();
        const pdfDoc = await pdfjsLib.getDocument(pdfData).promise;
        const totalPages = pdfDoc.numPages;
        const images = [];
        const targetDPI = 300;
        const scaleFactor = targetDPI / 72;  // PDF 默认分辨率通常是 72 DPI

        if (totalPages === 1) {
          const page = await pdfDoc.getPage(1);
          const canvas = document.createElement('canvas');
          const context = canvas.getContext('2d');
          const viewport = page.getViewport({scale: scaleFactor});
          canvas.width = viewport.width;
          canvas.height = viewport.height;

          // 将页面渲染到 canvas 上
          await page.render({
            canvasContext: context,
            viewport: viewport,
          }).promise;

          // 将 canvas 转换为 Base64 编码的图片（JPEG 格式）
          const imgDataUrl = canvas.toDataURL('image/jpeg');
          images.push(imgDataUrl);

          const keys = Object.keys(this.imagesList);
          if (keys.length > 25) {
            const firstKey = keys[0];
            delete this.imagesList[firstKey];
          }
          this.imagesList[nextDocUrl] = imgDataUrl;
        }

      } catch (error) {
        console.error('加载下一个图像失败', error);
      }
    },
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item--small) {
  margin-bottom: 5px;
}

:deep(.el-form-item) {
  margin-bottom: 15px;
}

.icon-with-text {
  display: flex;
  align-items: center;
  gap: 5px;
  white-space: nowrap;
  flex-shrink: 1;
  margin-left: 20px;
}

.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow-x: hidden;

  .header-bar {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    height: 48px;
    .icon {
      width: 28.28px;
      height: 22.89px;
    }

    .text {
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      letter-spacing: 0;
      margin-left: 10px;
    }
  }

  .bottom-area {
    display: flex;

    .left-content {
      display: flex;
      flex-direction: column;
      flex-shrink: 0;
      width: 720px;

      .step {
        width: 748px;
        height: 29.4px;
      }

      .bottom {
        width: 100%;
        display: flex;
        flex-direction: column;
        margin-top: 10px;
        margin-left: 12px;

        .upload-area {
          display: flex;
          gap: 30px;
          width: 100%;

          .upload-container {
            width: 330px;
            height: 82.6px;
            display: flex;
            flex-direction: column;
            border: none !important;
            
            :deep(.el-upload) {
              border: none !important;
            }

            :deep(.el-upload-dragger) {
              padding: 0;
              text-align: left;
              border: none !important;
              border-style: none !important;
            }

            .upload-item {
              width: 354px;
              height: 82.6px;
              background: #3981ff1a;
              border-radius: 6px;
              align-items: center;
              display: flex;
              cursor: pointer;
              transition: all 0.2s ease;
              
              &:hover {
                background: #3981ff2a;
                /* border: 2px solid #3981FF; */
                /* 去除边框加粗 */
                
                .right-content .title {
                  font-weight: 900;
                }
              }

              .el-upload-list {
                position: fixed !important;
                top: 10px;
              }

              .left-content {
                width: 60.58px;
                height: 58px;
                margin-left: 10px;
              }

              .right-content {
                display: flex;
                flex-direction: column;
                margin-left: 9px;
                width: 100%;
                align-items: flex-start;

                .title {
                  font-size: 16px;
                  color: #3981FF;
                  font-weight: bolder;
                  margin-top: 5px;
                  transition: font-weight 0.2s ease;
                }
                
                .title:hover {
                  font-weight: 900;
                }

                .subtitle {
                  font-size: 13px;
                  color: #999999;
                  letter-spacing: 0;
                }

                .speed-text {
                  font-size: 13px;
                  color: #409EFF;
                }
              }

            }
          }

        }
      }

      .description {
        display: flex;
        align-items: center;
        margin-left: 12px;

        .divider {
          width: 3px;
          height: 16px;
          top: 2.5px;
          border-radius: 2px 0 0 0;
          background: #3981FF;
        }

        .title {
          font-size: 15px;
          font-weight: bolder;
          line-height: 21px;
          text-align: left;
          margin-left: 6px;
        }
      }

      .file-area {
        margin-top: 29.5px;
        margin-left: 12px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        width: 800px;
        gap: 10px 68px;

        .hint-text, .model-hint {
          position: absolute;
          top: -20px;
          left: 90px;
          right: 0;
          font-size: 13px;
          color: #999999;
          font-style: italic;
          white-space: nowrap;
        }

        .item {
          width: 220px;
          height: 32px;

          /* 添加焦点状态下的边框加粗样式 */
          :deep(.el-input__wrapper.is-focus),
          :deep(.el-select__wrapper.is-focused),
          :deep(.el-input-number__wrapper.is-focus),
          :deep(.el-cascader.is-focus) {
            border: 2px solid var(--el-color-primary) !important;
            box-shadow: none !important;
          }

          /* 确保正常状态下的边框样式 */
          :deep(.el-input__wrapper),
          :deep(.el-select__wrapper),
          :deep(.el-input-number__wrapper),
          :deep(.el-cascader) {
            border: 1px solid var(--el-border-color);
            transition: border 0.2s ease;
          }
        }
      }

    }

    .right-content {
      height: 100%;
      padding: 0 0 10px 0;
      width: 100%;
      flex-shrink: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .canvas {
        height: 100%;
        width: 100%;
      }

      .pagination {
        height: 50px;
        margin-top: 5px;
        display: flex;
        align-items: center;

        .left {
          width: 200px;
          flex-shrink: 0;
        }

        .right {
          flex-grow: 1;
        }
      }
    }


  }

  .bottom-button-area {
    display: flex;
    height: 45px;
    width: 100%;
    flex-direction: row-reverse;
    gap: 24px;
    border-top: 2px solid #eeeeee;
    padding: 13px 0 0 0;

    .cancel-button {
      width: 74px;
      height: 32px;
      background: #FFFFFF;
      border: 1px solid #00000026;
      border-radius: 6px;

      .el-button__text {
        font-weight: 400;
        font-size: 14px;
        color: #000000e0;
        text-align: center;
        line-height: 22px;
      }
    }

    .confirm-button {
      width: 74px;
      height: 32px;
      background: #1677FF;
      border-radius: 6px;
      color: #FFFFFF !important;
      transition: background-color 0.3s ease;

      &:hover {
        background: #4a90ff !important;
      }
    }
  }

}
</style>