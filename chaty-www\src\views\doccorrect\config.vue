<template>
  <div class="main-wrapper">
    <el-page-header class="header-bar" @back="onBack">
      <template #content>
        <el-image src="/icon/16.png" class="icon"></el-image>
        <span class="header-title">试卷配置</span>
        <el-switch class="header-preview" v-model="isPreview" inactive-text="预览"/>
        <el-button v-if="isPreview" text icon="Refresh"/>

        <el-switch class="header-preview" v-if="isPreview" v-model="isPreviewAnswer" inactive-text="预览答案"/>
        <el-popover
            v-if="isPreview"
            placement="right-start"
            width="400"
            trigger="hover"
            content="答案位置在勾叉位置右侧，答案只有在设置勾叉后才显示。">
          <template #reference>
            <el-button style="margin-left: 5px" text icon="Warning"/>
          </template>
        </el-popover>

        <el-button v-if="cropped === true" text style="margin-left: 20px;"
                   @click="clearCropped">清空选择区域
        </el-button>
      </template>
      <template #extra>
        <div class="header-actions">
          <el-button class="clear-button" icon="Close" @click="clear">清空</el-button>
          <el-button class="header-action" type="primary" @click="showConfigs">试卷库</el-button>
          <el-dropdown @command="onSelectFile">
            <el-upload class="header-action" accept=".pdf" :action="$fileserver.pdf2imgUrl"
                       ref="uploadRef"
                       v-model:file-list="fileList"
                       :data="{ checkpageNum: true }"
                       :with-credentials="true"
                       :show-file-list="false"
                       :auto-upload="false"
                       :on-change="doUpload"
                       :http-request="uploadDocReq">
              <el-button type="primary">上传底卷</el-button>
            </el-upload>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="a">远程文件上传</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <ConfettiButton @click="clickSave"></ConfettiButton>
        </div>
      </template>
    </el-page-header>
    <div class="main-content">
      <div class="content-left">
        <el-card class="config-panel">
          <template #header>
            <div class="card-header">
              <div class="left-actions">
                <span>试卷信息</span>
                <!-- <el-tag>{{ config.docType }}</el-tag> -->
                <el-tag v-if="config.score && $refs.qsareaCard?.totalScore">{{
                    `总分：${$refs.qsareaCard?.totalScore}`
                  }}
                </el-tag>
                <el-tag v-for="item in $refs.qsareaCard?.scoreTypesAndTotalScore">{{ item.type }}:{{ item.score }}
                </el-tag>
                <el-tag v-if="config.score && $refs.qsareaCard?.additionalScore || 0 > 0">
                  {{ `附加：${$refs.qsareaCard.additionalScore}` }}
                </el-tag>
                <el-button icon="Plus" size="small" type="text" color="#3981ff" style="color:#3981ff" @click="addOneScoreTypes">添加</el-button>
              </div>

              <div class="right-actions">
                <el-tooltip placement="top" content="画完自动结束">
                  <el-button icon="Crop" text v-bind="flagsCropAttr" @click="startFlags">顺序画勾叉</el-button>
                </el-tooltip>
                <el-button type="primary" text icon="Setting" @click="showConfigForm">高级设置</el-button>
                <el-button type="warning" text :icon="configExpand === 'config' ? 'ArrowDown' : 'ArrowRight'"
                           @click="() => configExpand = configExpand === 'config' ? '' : 'config'"></el-button>
              </div>
            </div>
          </template>
          <el-collapse v-model="configExpand" accordion class="config-collapse">
            <el-collapse-item name="config">
              <el-form :model="config" class="config-card" label-width="120px" label-position="right"
                       inline style="padding: 20px">
                <el-form-item label="试卷名称：">
                  <el-input v-model="config.name" style="width: 300px"
                            placeholder="请输入试卷名称"></el-input>
                </el-form-item>
                <el-form-item label="试卷类型：">
                  <el-radio-group v-model="config.docType">
                    <el-radio value="8k">8k</el-radio>
                    <el-radio value="A4">A4</el-radio>
                    <el-radio value="A3">A3</el-radio>
                    <!--                        <el-radio label="b4">b4</el-radio>-->
                  </el-radio-group>
                </el-form-item>

                <el-form-item label="分数格式：">
                  <el-select v-model="config.scoreFormat" style="width: 300px" @change="refreshCropper">
                    <!--                    <el-option v-for="(_, index) in new Array(5)" :key="index" :value="index + 1"-->
                    <!--                               :label="`格式${index + 1}`"/> -->
                    <el-option :value="1" label="格式1-例如：听力40/40阅读60/60=总分100/100"/>
                    <el-option :value="6" label="仅显示分数-例如：36 56 92"/>
                    <el-option :value="7" label="扣分-例如：-5 -5 -10"/>
                  </el-select>
                </el-form-item>

                <el-form-item label="分数类型：">
                  <el-select v-model="config.scoreTypes" multiple filterable allow-create
                             default-first-option :reserve-keyword="false" placeholder="请输入分数类型"
                             @change="onScoreTypeChange" style="width: 250px;">
                    <el-option v-for="item in scoreTypeOptions" :key="item.value"
                               :label="item.label" :value="item.value"/>
                  </el-select>
                  <div style="display: flex">
                    <el-text style="margin-left: 10px">分数</el-text>
                    <el-switch v-model="config.score" style="margin: 0 0 0 10px"></el-switch>
                  </div>
                </el-form-item>

              </el-form>
            </el-collapse-item>
          </el-collapse>
        </el-card>

        <el-button icon="Crop" v-bind="nameCropAttr" class="name-button"
                   @click="crop('name', { area: config.nameArea })">姓名
        </el-button>
        <el-button icon="Crop" v-bind="studentNumberCropAttr" class="student-number-button"
                   @click="crop('studentNumber', { area: config.studentNumberArea })">学号
        </el-button>
        <el-button icon="Crop" v-bind="totalScoreCropAttr" class="total-score-button"
                   @click="crop('totalScore', { area: config.totalScoreArea })">等级
        </el-button>
        <el-button icon="Crop" v-bind="scoreCropAttr" v-show="config.score" class="score-button"
                   @click="crop('score', { area: config.scoreArea })">分数
        </el-button>
        <el-button v-if="areaCropLoading" v-bind="scoreCropAttr" v-show="config.score" class="ocr-button"
                   @click="startOcr">
          <el-image v-if="!areaCropOcrLoading" src="/icon/ai.svg" class="icon"></el-image>
          <el-icon v-else class="icon">
            <Loading/>
          </el-icon>
          <el-text v-if="!areaCropOcrLoading" class="text">开始识别</el-text>
          <el-text v-else class="text">题目识别中</el-text>
        </el-button>
        <qsarea-card class="card-qsarea"
                     header="试卷题目"
                     ref="qsareaCard"
                     :cropOptions="cropOptions"
                     :isScore="config.score"
                     :qsOcrPrompt="config.qsOcrPrompt"
                     :scoreTypes="config.scoreTypes"
                     :config="config"
                     :extractingAreas="$refs.cropper ? $refs.cropper.isExtracting : []"
                     @crop="crop"
                     @stopOcr="stopOcr"
                     @refreshCropper="qsareaRefresh"
                     @isExtracting="isExtracting"
        />
      </div>
      <cropper
          ref="cropper"
          class="content-left"
          :id="configId || 'temp-config'"
          :is-freeze-keyboard="false"
          :left-top-text="leftTopText"
          isConfig
          @onCropened="onCropped"
          @clickOrder="handlePointsUpdated"
          @empty-right-clicked="emptyRightClicked"
          @button-crop-completed="buttonCropCompleted"
          @auto-crop-completed="autoCropCompleted"
      >
      </cropper>
      
      <!-- 勾叉/字体 大小/透明度按钮 -->
      <el-tooltip class="item" effect="dark"
                  content="勾叉大小、字体大小和透明度设置" placement="top">
          <span class="icon-with-text">
            <el-icon @click="showXYOffsetDisplacementDialog" color="#007bff" size="20px">
              <Operation/>
            </el-icon>
            <span @click="showXYOffsetDisplacementDialog" class="tooltip-text">勾叉/字体 大小/透明度</span>
          </span>
      </el-tooltip>
    </div>

    <canvas ref="uploadCanvas" id="correct-canvas" style="display: none;"></canvas>
    <config-selector ref="configSelector" @onSelect="onConfigSelect"/>
    <areas-selector ref="areasSelector" @onSelect="onAreasSelect"/>
    <config-form ref="configForm" :default-qs-ocr-prompt="defaultQsOcrPrompt"
                 @submit="submitConfigForm"></config-form>

    <img src="/flag.svg" id="flagimg" style="display: none;"/>
    <img :src="docImg" id="img" style="display: none;"/>

    <file-selector ref="fileSelector" @file-selected="onFileSelected"/>
    
    <!-- XYOffsetDisplacementDialog组件 -->
    <XYOffsetDisplacementDialog 
        :id="configId || 'temp-config'"
        :ids="[configId || 'temp-config']"
        set-mark-zoom
        :showPageSelector="false"
        :showOffset="false"
        :useUpdateInterface="true"
        ref="XYOffsetDisplacementDialog"
        @submit="handleXYOffsetDisplacementDialog"
        @refresh="refreshCropper"
        @close="unFreezeKeyBoard">
    </XYOffsetDisplacementDialog>
  </div>
</template>
<script>
import Cropper from '@/components/cropper/forPreview.vue';
import {h} from 'vue';
import {img2base64} from '../../utils/imgUtil';
import ConfigForm from "./components/configform.vue";
import ConfigSelector from './components/configselector.vue';
import QsareaCard from './components/qsareacard.vue';
import MessageContent from '../../components/MessageContent.vue';
import AreasSelector from './components/areasSelector.vue';
import FileSelector from '../common/FileSelector.vue';
import {useUserStore} from "@/store";
import ConfettiButton from '@/components/inspiraUI/ConfettiButton.vue'
import question from "@/views/question/index.vue";
import XYOffsetDisplacementDialog from '@/views/common/XYOffsetDisplacementDialog.vue'
import {Operation} from "@element-plus/icons-vue";

const store = useUserStore();
export default {

  components: {
    Cropper, QsareaCard, ConfigSelector, ConfigForm, AreasSelector, FileSelector, ConfettiButton, XYOffsetDisplacementDialog, Operation
  },
  data() {
    return {
      // defaultQsOcrPrompt: "你是一位对任何事都一步一步思考的阅卷老师，你的任务是依次识别每一小题的题目、题目信息和答案并针对每一道小题依次返回结果。\n在批改过程中，请严格遵守以下规则：1. 如果一道填空题中包含多个空格，请把每一个空格当做一道独立的题目进行题目识别。\n2. 如果一道大题中包含多个小问，请把每一个小问当做一道独立的题目进行识别。\n3. 题目中应包含，题目的题干信息，使得大模型能够定位到题目。例如:(   ) 1. A.favourite B.finish C.fish。\n4. 题目信息中应包含，题目的位置信息与学生答案的类型与样例，使得大模型能够了解到需要识别的内容，提高识别准确率。例如:题目前括号内为学生答案，学生答案为一个大写英文字母，例如'A'或'B'或'C'，例如'A'。(   ) 1. A.favourite B.finish C.fish。\n5. 答案中应包含识别到的最终答案，对于复杂题型请通过文字描述答案，使得大模型能够做出高质量的批改。例如:'A'\n在识别过程中，请保持一步一步思考，保持耐心和细致，体现对学生的关怀和支持。" 请将图片中的所有题目依次按照这样的格式回答：\\n---\\n## 题目\\n3x - 3.9 =\\n## 答案\\n10\\n## 答案的相关信息\\n图中(从上往下数)第一行为题目，题目下方最后一行为学生答案，学生答案为一个数学数字\\n3x - 3.9 =\\n\\n---\\n## 题目\\n3M + 8.7 = (   )\\n## 答案\\n10\\n## 答案的相关信息\\n图中题目中括号内的为学生答案，学生答案为一个数学数字\\n3M + 8.7 = (   )\\n\\n---\\n## 题目\\n(   ) 4.\\n## 答案\\n10\\n## 答案的相关信息\\n图中从上往下数第2个括号，括号内为学生答案，学生答案为'√'或者'×'\\n(   ) 4.",
      defaultQsOcrPrompt: null,
      config: {
        name: "",
        docurl: "",
        img: "",
        score: true,
        scoreFormat: 1,
        scoreColor: 'red',
        scoreFontSize: 10,
        fontSize: 10,
        flagSize: 20,
        flagColor: 'red',
        errorFlagColor: 'red',
        errorFlagSize: 20,
        errorFlag: 'x',
        correctFlag: 'a',
        docType: '8k',
        nameArea: null,
        scoreArea: null,
        totalScoreArea: null,
        additionalName: '附加',
        prompt: null,
        qsOcrPrompt: this.defaultQsOcrPrompt,
        scoreTypes: ['总分'],
        ranges: {},
        enableLevelOutput: false,
        studentNameRightRotation: 0,
        studentNumberRightRotation: 0
      },
      docImg: null,
      cropOptions: {
        type: null,
        qsIdx: null,
      },
      isPreview: false,
      cropped: false,
      configExpand: '',// 默认展开的话设置为config
      scoreTypeOptions: [
        {value: '总分', label: '总分'}
      ],
      flagsCropAttr: {
        type: "primary",
      },
      uploadDocMsg: null,
      uploadProgress: 0,
      isPreviewAnswer: true,
      fileList: [],
      areaCropLoading: false,
      areaCropOcrLoading: false,
      configId: '',
      selectedAreaIndexes: [],
      selectedNowIndexes: -1,
      leftTopText: '',
      packageId: null,
      XYOffsetData: {
        xOffset: 0,
        yOffset: 0
      }
    }
  },
  mounted() {
    this.defaultQsOcrPrompt = JSON.parse(JSON.stringify(store.getDefaultConfigs['EXTRA_QS_RESPFORMAT_TEMPLETE']));
  },
  destroyed() {
    document.removeEventListener('keydown', this.onKeyDown, false)
  },
  computed: {
    nameCropAttr() {
      return this.cropStatus('name', this.config.nameArea)
    },
    studentNumberCropAttr() {
      return this.cropStatus('studentNumber', this.config.studentNumberArea)
    },
    totalScoreCropAttr() {
      return this.cropStatus('totalScore', this.config.totalScoreArea)
    },
    scoreCropAttr() {
      return this.cropStatus('score', this.config.scoreArea)
    }
  },
  watch: {
    'config.scoreArea': {
      handler(newVal) {
        if (this.isPreview) {
          this.refreshCropper();
        }
      }
    },
    'config.nameArea': {
      handler(newVal) {
        if (this.isPreview) {
          this.refreshCropper();
        }
      }
    },
    'config.studentNumberArea': {
      handler(newVal) {
        if (this.isPreview) {
          this.refreshCropper();
        }
      }
    },
    'config.totalScoreArea': {
      handler(newVal) {
        if (this.isPreview) {
          this.refreshCropper();
        }
      }
    },
    'config.enableLevelOutput': {
      handler(newVal) {
        this.checkEnableLevelOutput()
      }
    },
    isPreview(val) {
      this.preview()
    },
    isPreviewAnswer(val) {
      if (val) {
        this.preview()
      } else {
        this.refreshCropper()
      }
    },
    selectedNowIndexes(val) {
      if (val === -1) {
        this.leftTopText = '';
        return;
      }
      // 计算当前第几个区域的第几个题目
      const area = this.$refs.qsareaCard.areas;
      const selectedAreaIndexes = this.selectedAreaIndexes;
      const selectedNowIndexes = val;
      let pointsIdx = 0;
      for (let areaIdx = 0; areaIdx < area.length; areaIdx++) {
        if (!selectedAreaIndexes.includes(areaIdx)) {
          continue;
        }
        for (let qsIdx = 0; qsIdx < area[areaIdx].questions.length; qsIdx++) {
          if (selectedNowIndexes === pointsIdx) {
            this.leftTopText = `请画区域${areaIdx + 1}的第${qsIdx + 1}个题目`;
            pointsIdx++;
            break;
          }
          pointsIdx++;
        }
        if (pointsIdx >= selectedAreaIndexes) break;
      }
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      let id = to.query.id
      let packageId = to.query.packageId
      if (id) {
        vm.loadConfig(id, packageId)
      }
    })
  },
  methods: {
    isExtracting(areaIdx) {
      const arr = this.$refs.cropper.isExtracting;
      const idx = arr.indexOf(areaIdx);
      if (idx === -1) {
        arr.push(areaIdx);
      } else {
        arr.splice(idx, 1);
      }
      this.$refs.cropper.isExtracting = arr;
      this.$refs.cropper.scheduleRender();
    },
    autoCropCompleted(e, isUseOcr, isScorePoint) {
      this.$refs.qsareaCard.addArea(e, isUseOcr, isScorePoint)
    },
    emptyRightClicked(areaIdx, flagArea) {
      const area = this.$refs.qsareaCard.areas;
      if (areaIdx >= area.length) {
        return;
      }
      let haveFind = false;
      for (let qsIdx = 0; qsIdx < area[areaIdx].questions.length; qsIdx++) {
        if (!('flagArea' in area[areaIdx].questions[qsIdx]) || !area[areaIdx].questions[qsIdx].flagArea || (area[areaIdx].questions[qsIdx].flagArea.x === 0 && area[areaIdx].questions[qsIdx].flagArea.y === 0)) {
          this.$message.success('第' + (areaIdx + 1) + '区域的第' + (qsIdx + 1) + '题的勾叉已经画上')
          this.$refs.qsareaCard.setArea('flag', flagArea, areaIdx, qsIdx);
          haveFind = true
          break;
        }
      }
      if (!haveFind) {
        this.$message.warning(`区域${areaIdx + 1}的勾差已经全部画完`)
      }
    },
    qsareaRefresh(e) {
      this.preview(e);
    },
    clickSave() {
      this.doSave();
    },
    refreshCropper() {
      this.preview();
    },
    checkEnableLevelOutput() {
      if (this.config.enableLevelOutput && (!this.config.totalScoreArea || !('x' in this.config.totalScoreArea))) {
        this.$message.warning("请框出等级区域")
        return false;
      }
      return true;
    },
    startOcr() {
      if (!this.areaCropOcrLoading) {
        this.crop('area', {areaIdx: this.cropOptions.areaIdx});
        this.$refs.qsareaCard.startOcr(this.cropOptions.areaIdx);
        // 多个一起识别
        this.areaCropOcrLoading = false;
      }
    },
    stopOcr() {
      this.areaCropOcrLoading = false;
    },
    showAreasSelector() {
      this.$refs.areasSelector.show(this.$refs.qsareaCard.areas)
    },
    onAreasSelect(e) {
      this.selectedAreaIndexes = e;
      this.selectedNowIndexes = 0;
      this.$refs.cropper.isClickOrder = true;
      this.$refs.cropper.changeShowDetail(true);
      this.flagsCropAttr.type = 'warning'
    },
    handlePointsUpdated(point) {
      const area = this.$refs.qsareaCard.areas;
      const selectedAreaIndexes = this.selectedAreaIndexes;
      const selectedNowIndexes = this.selectedNowIndexes;
      let pointsIdx = 0;
      for (let areaIdx = 0; areaIdx < area.length; areaIdx++) {
        if (!selectedAreaIndexes.includes(areaIdx)) {
          continue;
        }
        for (let qsIdx = 0; qsIdx < area[areaIdx].questions.length; qsIdx++) {
          if (selectedNowIndexes === pointsIdx) {
            this.$message.success('第' + (areaIdx + 1) + '区域的第' + (qsIdx + 1) + '题的勾叉已经画上')
            this.$refs.qsareaCard.setArea('flag', point, areaIdx, qsIdx);
            this.selectedNowIndexes = selectedNowIndexes + 1;
            pointsIdx++;
            break;
          }
          pointsIdx++;
        }
        if (pointsIdx >= selectedAreaIndexes) break;
      }
      // 检查有没有全部画完，有则主动结束
      let areaQsSize = area.reduce((prev, cur, index) => {
        return prev + (selectedAreaIndexes.includes(index) ? cur.questions.length : 0)
      }, 0);
      if (areaQsSize === selectedNowIndexes + 1) {
        this.selectedNowIndexes = -1;
        this.$refs.cropper.showDetail = false;
        this.$refs.cropper.changeShowDetail(false);
        this.$refs.cropper.isClickOrder = false;
        this.flagsCropAttr.type = 'primary';
        this.$message.success('所有的勾叉已经画完')
      }
    },
    startFlags() {
      if (this.flagsCropAttr.type === 'warning') {
        // 点击关闭
        this.selectedNowIndexes = -1;
        this.$refs.cropper.isClickOrder = false;
        this.flagsCropAttr.type = 'primary';
        this.$refs.cropper.changeShowDetail(false);
      } else if (this.flagsCropAttr.type === 'primary') {
        // 点击开始
        this.showAreasSelector();
      }
    },
    onBack() {
      this.$router.back();
    },
    uploadDoc(response) {
      this.uploadDocMsg?.close()
      this.uploadDocMsg = null
      if (response.code !== 200) {
        this.$message.error('上传文档失败')
        return
      } else {
        this.$message.success('上传文档成功')
      }
      const code = response.data.code
      if (code === 2) {
        this.confirmPDFPage(response.data)
      } else if (code === 1) {
        this.createConfig(response.data)
      }

    },
    addOneScoreTypes() {
      this.$prompt(`请输入要新增的分数类型。`, '提示', {
        confirmButtonText: '新增',
        cancelButtonText: '取消'
      }).then(({value}) => {
        if (value) {
          this.config.scoreTypes.push(value)
        }
      })
    },
    confirmPDFPage(params) {
      const {pageNum, filename} = params
      this.$prompt(`请确认页码, 当前PDF共${pageNum}页。`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        inputPattern:
            /\d+/,
        inputErrorMessage: '请输入数字',
        inputValidator: (val) => {
          if (val < 1 || val > pageNum) {
            return `请输入正确的页码!`
          }
          return true
        }
      }).then(({value}) => {
        this.$axios.get(`/api/file/pdf2Img?filename=${filename}&pageNum=${value}`).then(res => {
          this.createConfig(res.data)
        })
      })
    },
    createConfig(params) {
      this.config.docurl = params.docpath
      this.config.img = params.url
      this.isPreview = false
      img2base64(this.$fileserver.fileurl(this.config.img)).then(res => {
        this.docImg = res
        this.$refs.cropper.setImg(res)
      })
    },
    cropStatus(type, area) {
      if (this.cropOptions.type === type) {
        return {
          type: "warning",
        }
      } else if (area) {
        return {
          type: "success",
        }
      } else {
        return {
          type: "primary",
        }
      }
    },
    buttonCropCompleted(cropped) {
      const {
        type,
        area,
        areaIdx,
        qsIdx,
        areaType
      } = this.cropOptions;
      this.cropped = false

      if (cropped !== null && cropped.width === 0 && cropped.height === 0) {
        cropped = null
      }
      let ops = Promise.resolve()
      if (type === 'name') {
        this.config.nameArea = cropped
      }
      if (type === 'studentNumber') {
        this.config.studentNumberArea = cropped
      }
      if (type === 'score') {
        this.config.scoreArea = cropped
      }
      if (type === 'totalScore') {
        this.config.totalScoreArea = cropped
      }
      if (type === 'area') {
        this.areaCropLoading = false;
      }
      if (['area', 'review', 'flag', 'option', 'qsPos'].includes(type)) {
        this.$refs.qsareaCard.setArea(type, cropped, areaIdx, qsIdx)
      }

      ops.finally(() => {
        this.cropOptions = {
          type: null,
          qsIdx: null,
          areaIdx: null,
        }
      })
    },
    crop(type, {
      area,
      areaIdx,
      qsIdx,
      areaType
    }) {
      if (this.cropOptions.type === type && this.cropOptions.qsIdx === qsIdx && this.cropOptions.areaIdx === areaIdx) {
        this.cropped = false
        let cropped = this.$refs.cropper.unCrop();

        if (cropped !== null && cropped.width === 0 && cropped.height === 0) {
          cropped = null
        }
        let ops = Promise.resolve()
        if (type === 'name') {
          this.config.nameArea = cropped
        }
        if (type === 'studentNumber') {
          this.config.studentNumberArea = cropped
        }
        if (type === 'score') {
          this.config.scoreArea = cropped
        }
        if (type === 'totalScore') {
          this.config.totalScoreArea = cropped
        }
        if (type === 'area') {
          this.areaCropLoading = false;
        }
        if (['area', 'review', 'flag', 'option', 'qsPos'].includes(type)) {
          this.$refs.qsareaCard.setArea(type, cropped, areaIdx, qsIdx)
        }

        ops.finally(() => {
          this.cropOptions = {
            type: null,
            qsIdx: null,
            areaIdx: null,
          }
        })
      } else {
        if (type === 'area' && areaType !== 4) {
          this.areaCropLoading = true;
        }
        this.cropOptions = {
          type,
          area,
          areaIdx,
          qsIdx,
          areaType
        }
        if (area) {
          this.cropped = true
        }
        this.$refs.cropper.doCrop(area)
      }
    },
    clear() {
      this.cropped = false
      this.config = {
        name: "",
        studentNumber: "",
        docurl: "",
        img: "",
        score: true,
        scoreFormat: 1,
        scoreColor: 'red',
        scoreFontSize: 10,
        fontSize: 10,
        flagSize: 20,
        flagColor: 'red',
        errorFlagColor: 'red',
        errorFlagSize: 20,
        errorFlag: 'x',
        correctFlag: 'a',
        docType: store.getDefaultDocType,
        nameArea: null,
        studentNumberArea: null,
        totalScoreArea: null,
        scoreArea: null,
        additionalName: '附加',
        prompt: null,
        qsOcrPrompt: null,
        scoreTypes: ['总分'],
        enableLevelOutput: false,
        studentNumberRightRotation: 0,
        studentNameRightRotation: 0
      }
      this.docImg = null
      this.cropOptions = {
        type: null,
        areaIdx: null,
        qsIdx: null
      }
      this.$refs.qsareaCard.setData([
        {
          areaType: 1,
          area: null,
          enabled: true,
          opinion: 1,
          questions: [
            {
              name: "问题1",
              question: "",
              qsInfo: "",
              answer: "",
              score: 1,
              isAdditional: 1,
              flagArea: {
                x: 0,
                y: 0,
                width: 50,
                height: 50,
                rotate: 0,
                scaleX: 1,
                scaleY: 1
              },
              reviewType: 2,
              defaultReview: '',
              scoreType: '总分',
              opinion: 2,
              isScorePoint: 1,
              scorePoints: "",
              dontShowText: false
            },
          ],
          expand: [],
        },
      ])
      this.isPreview = false
      this.scoreTypeOptions = [
        {value: '总分', label: '总分'}
      ]
    },
    preview(data = null) {
      if (!this.docImg) {
        return
      }
      let detail = null;
      if (data) {
        detail = data;
      } else {
        detail = this.$refs.qsareaCard.areas;
      }

      let answers = [];
      let areas = [];
      let kuangs = [];
      let scopeTypes = ['总分'];
      let scopeTypesScore = {
        '总分': {scored: 0.0, fullMarks: 0.0}
      };
      const isPreviewAnswer = this.isPreviewAnswer;
      
      // 获取当前的字体大小设置
      const currentFontSize = store.getFontSizeDataById(this.configId || 'temp-config') || 100;
      
      detail.forEach(detailItem => {
        detailItem.questions.forEach(area => {
          if (area.flagArea) {
            areas.push(Object.assign({
              color: 'green',
              mark: document.getElementById("flagimg"),
              isScorePoint: area.isScorePoint,
              text: area.answer === '无' ? area.score : area.answer,
            }, area.flagArea))
            if (isPreviewAnswer) {
              answers.push({
                area: area.flagArea,
                text: area.answer === '无' ? (area.score + (area.isScorePoint === 2 ? `/${area.score}` : '')) : area.answer,
                dontShowText: area?.dontShowText ?? false,
                font: `${currentFontSize}px Arial`,
                score: area.score
              })
            }

          }
          if (!scopeTypes.includes(area.scoreType)) {
            scopeTypes.push(area.scoreType)
          }
          let scored = Number(area?.score ?? 0.0);
          let fullMarks = Number(area?.score ?? 0.0);
          if (area.scoreType !== '总分') {

            if (!(area.scoreType in scopeTypesScore)) {
              scopeTypesScore[area.scoreType] = {scored: 0.0, fullMarks: 0.0}
            }
            scopeTypesScore[area.scoreType].scored += scored;
            scopeTypesScore[area.scoreType].fullMarks += fullMarks;
          }
          scopeTypesScore['总分'].scored += scored;
          scopeTypesScore['总分'].fullMarks += fullMarks;
        })
        // 画框
        kuangs.push({
          area: detailItem.area,
          color: detailItem.areaType === 4 ? 'red' : '',
          qsCnt: detailItem.questions.length,
          qsAllScore: detailItem.questions.reduce((total, question) => {
            return total + (question.score ?? 0.0)
          }, 0.0),
          scoreTypes: detailItem.questions[0].scoreType,
          commonQuestionType: detailItem.areaType === 1 ? detailItem?.commonQuestionType : '',
        })
      })
      // 将scoreType的总分调整到最后
      scopeTypes = [...scopeTypes.slice(1), scopeTypes[0]];

      // 姓名/学号也展示文字
      const configObj = this.config;
      if (configObj.nameArea && 'x' in configObj.nameArea) {
        answers.push({
          area: configObj.nameArea,
          text: '姓名',
          fillStyle: 'blue',
          type: 'identify',
          offsetX: 80,
          offsetY: 20,
        })

        kuangs.push({
          area: configObj.nameArea,
          type: 'identify',
          color: '#8A2BE2'
        })
      }
      if (configObj.studentNumberArea && 'x' in configObj.studentNumberArea) {
        answers.push({
          area: configObj.studentNumberArea,
          text: '学号',
          fillStyle: 'blue',
          type: 'studentNumber',
          offsetX: 80,
          offsetY: 20
        })

        kuangs.push({
          area: configObj.studentNumberArea,
          type: 'studentNumber',
          color: '#8A2BE2'
        })
      }
      // 等级
      if (configObj.totalScoreArea && 'x' in configObj.totalScoreArea) {
        // kuangs.push({
        //   area: configObj.totalScoreArea,
        //   color: '#e8a3a3',
        //   type: 'totalScoreArea',
        // })
        // 等级输出
        let text = "    ";
        for (let i = 0; i < scopeTypes.length; i++) {
          text += `A-x   `;
        }
        answers.push({
          area: {
            x: configObj.totalScoreArea.x,
            y: configObj.totalScoreArea.y,
            width: 1,
            height: 1
          },
          text: text,
          fillStyle: 'red',
          type: 'totalScoreAreaText',
          offsetX: 1,
          offsetY: 1,
          font: `italic ${currentFontSize}px Arial`
        })
      }
      if (configObj.score && configObj.scoreArea && 'x' in configObj.scoreArea) {
        // kuangs.push({
        //   area: configObj.scoreArea,
        //   color: '#e8a3a3',
        //   type: 'scoreArea',
        // })
        // 现在有三种类型
        let text = "";
        const scoreFormat = configObj.scoreFormat;
        if (scoreFormat === 6) {
          let totalScore = 0.0;
          for (let i = 0; i < scopeTypes.length; i++) {
            text += `${scopeTypesScore[scopeTypes[i]].scored} `;
          }
        } else if (scoreFormat === 7) {
          for (let i = 0; i < scopeTypes.length; i++) {
            text += `-${scopeTypesScore[scopeTypes[i]].fullMarks - scopeTypesScore[scopeTypes[i]].scored} `;
          }
        } else {
          for (let i = 0; i < scopeTypes.length; i++) {
            text += `${scopeTypes[i]}:${scopeTypesScore[scopeTypes[i]].scored}/${scopeTypesScore[scopeTypes[i]].fullMarks}`;
          }
        }
        answers.push({
          area: {
            x: configObj.scoreArea.x,
            y: configObj.scoreArea.y,
            width: 1,
            height: 1
          },
          text: text,
          fillStyle: 'red',
          type: 'scoreAreaText',
          offsetX: 1,
          offsetY: 1,
          font: `${currentFontSize}px Arial`
        })
      }


      this.$refs.cropper.setImg(this.docImg, areas, answers, kuangs);
    },
    showConfigs() {
      this.$refs.configSelector.show()
    },
    onConfigSelect(selected) {
      this.clear()
      let configObj = selected.config ? JSON.parse(selected.config) : {}
      let config = {
        id: selected.id,
        name: selected.name,
        studentNumber: selected.studentNumber || '',
        docurl: selected.docurl,
        img: selected.img,
        docType: selected.docType,
        score: configObj.score,
        scoreFormat: configObj.scoreFormat || 1,
        scoreColor: configObj.scoreColor || 'red',
        scoreFontSize: configObj.scoreFontSize || 10,
        fontSize: configObj.fontSize,
        flagSize: configObj.flagSize,
        flagColor: configObj.flagColor || 'red',
        errorFlagColor: configObj.errorFlagColor || 'red',
        errorFlagSize: configObj.errorFlagSize || 20,
        errorFlag: configObj.errorFlag || 'x',
        correctFlag: configObj.correctFlag || 'a',
        nameArea: configObj.nameArea,
        studentNumberArea: configObj.studentNumberArea || null,
        totalScoreArea: configObj.totalScoreArea || null,
        scoreArea: configObj.scoreArea,
        additionalName: configObj.additionalName || '附加',
        prompt: configObj.prompt || null,
        qsOcrPrompt: configObj.qsOcrPrompt || this.defaultQsOcrPrompt,
        scoreTypes: configObj.scoreTypes || [],
        ranges: configObj.ranges || {},
        enableLevelOutput: configObj.enableLevelOutput,
        studentNameRightRotation: configObj.studentNameRightRotation || 0,
        studentNumberRightRotation: configObj.studentNumberRightRotation || 0
      }
      
      // 从数据库的pageSettings中读取markZoom、fontSize、opacity参数
      const configId = selected.id;
      if (selected.pageSettings) {
        try {
          const pageSettings = JSON.parse(selected.pageSettings);
          
          // 设置markZoom参数
          if (pageSettings.markZoom !== undefined) {
            store.setMarkZoomDataById(configId, parseInt(pageSettings.markZoom) || 80);
          } else {
            // 如果没有pageSettings数据，设置默认值
            store.setMarkZoomDataById(configId, 80);
          }
          
          // 设置fontSize参数
          if (pageSettings.fontSize !== undefined) {
            store.setFontSizeDataById(configId, parseInt(pageSettings.fontSize) || 80);
          } else {
            // 如果没有pageSettings数据，设置默认值
            store.setFontSizeDataById(configId, 80);
          }
          
          // 设置opacity参数
          if (pageSettings.opacity !== undefined) {
            store.setOpacityDataById(configId, parseFloat(pageSettings.opacity) || 0.6);
          } else {
            // 如果没有pageSettings数据，设置默认值
            store.setOpacityDataById(configId, 0.6);
          }
        } catch (error) {
          console.error('解析pageSettings失败:', error);
          // 解析失败时设置默认值
          store.setMarkZoomDataById(configId, 80);
          store.setFontSizeDataById(configId, 80);
          store.setOpacityDataById(configId, 0.6);
        }
      } else {
        // 如果没有pageSettings字段，设置默认值
        store.setMarkZoomDataById(configId, 80);
        store.setFontSizeDataById(configId, 80);
        store.setOpacityDataById(configId, 0.6);
      }
      
      let scoreTypeOptions = []
      config.scoreTypes.forEach(type => {
        scoreTypeOptions.push({
          value: type,
          label: type
        })
      })
      if (!config.scoreTypes.find(item => item === '总分')) {
        config.scoreTypes.unshift('总分')
        scoreTypeOptions.unshift({
          value: '总分',
          label: '总分'
        })
      }
      this.scoreTypeOptions = scoreTypeOptions
      this.config = config
      if (this.config.img) {
        img2base64(this.$fileserver.fileurl(this.config.img)).then(res => {
          this.docImg = res
          this.isPreview = true;
          this.preview();
        })
      }
      let areas = selected.areas ? JSON.parse(selected.areas) : []
      this.$refs.qsareaCard.setData(areas)
    },
    doSave(dontShowMessage) {
      if (!this.checkEnableLevelOutput()) {
        return;
      }
      // Initialize default ranges if not set
      if (this.config.enableLevelOutput && (!this.config.ranges || !Object.keys(this.config.ranges).length)) {
        // Get score types and their max scores from qsareaCard
        const scoreTypesAndScores = this.$refs.qsareaCard?.scoreTypesAndTotalScore || [];
        // Initialize ranges for each score type
        this.config.ranges = {};

        // Add ranges for each score type
        scoreTypesAndScores.forEach(({type, score}) => {
          this.config.ranges[type] = [
            {maxx: score, minn: Math.round(score * 0.9), name: 'A', sliderRange: [0, 10]},
            {maxx: Math.round(score * 0.89), minn: Math.round(score * 0.6), name: 'B', sliderRange: [10, 40]},
            {maxx: Math.round(score * 0.59), minn: 0, name: 'C', sliderRange: [40, 100]}
          ];
        });

        // Add range for total score if not already present
        if (!this.config.ranges['总分']) {
          const totalScore = this.$refs.qsareaCard?.totalScore || 100;
          this.config.ranges['总分'] = [
            {maxx: totalScore, minn: Math.round(totalScore * 0.9), name: 'A', sliderRange: [0, 10]},
            {maxx: Math.round(totalScore * 0.89), minn: Math.round(totalScore * 0.6), name: 'B', sliderRange: [10, 40]},
            {maxx: Math.round(totalScore * 0.59), minn: 0, name: 'C', sliderRange: [40, 100]}
          ];
        }
      }
      let form = {
        id: this.config.id,
        name: this.config.name,
        studentNumber: this.config.studentNumber,
        docurl: this.config.docurl,
        img: this.config.img,
        docType: this.config.docType,
        config: JSON.stringify({
          score: this.config.score,
          scoreFormat: this.config.scoreFormat,
          scoreColor: this.config.scoreColor,
          scoreFontSize: this.config.scoreFontSize,
          fontSize: this.config.fontSize,
          flagSize: this.config.flagSize,
          flagColor: this.config.flagColor,
          errorFlagColor: this.config.errorFlagColor,
          errorFlagSize: this.config.errorFlagSize,
          errorFlag: this.config.errorFlag,
          correctFlag: this.config.correctFlag,
          nameArea: this.config.nameArea,
          studentNumberArea: this.config.studentNumberArea,
          totalScoreArea: this.config.totalScoreArea,
          scoreArea: this.config.scoreArea,
          additionalName: this.config.additionalName,
          prompt: this.config.prompt,
          qsOcrPrompt: this.config.qsOcrPrompt,
          scoreTypes: this.config.scoreTypes,
          ranges: this.config.ranges,
          enableLevelOutput: this.config.enableLevelOutput,
          studentNumberRightRotation: this.config.studentNumberRightRotation,
          studentNameRightRotation: this.config.studentNameRightRotation
        }),
        areas: JSON.stringify(this.$refs.qsareaCard.areaData()),
        // 保存pageSettings参数到数据库
        pageSettings: JSON.stringify({
          markZoom: store.getMarkZoomDataById(this.config.id) || 80,
          fontSize: store.getFontSizeDataById(this.config.id) || 80,
          opacity: store.getOpacityDataById(this.config.id) || 0.6
        })
      }
      // 校验areas,根据areas校验
      let areas = JSON.parse(form.areas);
      for (let i = 0; i < areas.length; i++) {
        let areaType = areas[i].areaType;
        for (let j = 0; j < areas[i].questions.length; j++) {
          if (areaType === 4) {
            areas[i].questions[j].isScorePoint = 2;
          } else if (areaType === 1) {
            areas[i].questions[j].isScorePoint = 1;
          }
        }
      }
      form.areas = JSON.stringify(areas)
      this.$axios.post("/api/docCorrectConfig/update", form).then(res => {
        if (res.data.id !== this.config.id) {
          this.config.id = res.data.id
        }
        if (!dontShowMessage) {
          this.$message.success('已保存')
        }
      })
      /* if (this.config.id) {

      } else {
          this.$axios.post("/api/docCorrectConfig/add", form).then(res => {
              this.config.id = res.data.id
              this.$message.success('新增成功')
          })
      } */
    },
    loadConfig(id, packageId) {
      this.configId = id;
      this.packageId = packageId;
      this.config = {
        id: id,
        name: "",
        studentNumber: "",
        docurl: "",
        img: "",
        score: true,
        scoreFormat: 1,
        scoreColor: 'red',
        scoreFontSize: 10,
        fontSize: 10,
        flagSize: 20,
        flagColor: 'red',
        errorFlagColor: 'red',
        errorFlagSize: 20,
        errorFlag: 'x',
        correctFlag: 'a',
        docType: '8k',
        nameArea: null,
        studentNumberArea: null,
        totalScoreArea: null,
        scoreArea: null,
        additionalName: '附加',
        prompt: null,
        qsOcrPrompt: this.defaultQsOcrPrompt,
        scoreTypes: ['总分'],
        ranges: {},
        enableLevelOutput: false,
        studentNameRightRotation: 0,
        studentNumberRightRotation: 0
      };
      this.$axios.get(`/api/docCorrectConfig/get?id=${id}`).then(res => {
        if (res.data) {
          this.$nextTick(() => {
            this.onConfigSelect(res.data)
          })
        }
      })
    },
    onKeyDown(e) {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        // 执行save方法
        this.doSave()
        // 阻止默认事件
        e.preventDefault()
      }
    },
    onCropped() {
      this.cropped = true
    },
    clearCropped() {
      this.$refs.cropper.clearCropped()
    },
    showConfigForm() {
      // this.doSave();
      this.$refs.configForm.show(this.config, this.configId, this.packageId)
    },
    submitConfigForm(form) {
      this.config = Object.assign(this.config, form)
      // this.doSave();
    },
    onScoreTypeChange(tags) {
      if (!tags.includes('总分')) {
        this.$message.warning('总分不能删除')
        this.$nextTick(() => {
          this.config.scoreTypes.unshift('总分')
        })
      }
    },
    uploadDocReq(options) {
      this.uploadDocMsg = this.$message({
        message: () => h(MessageContent, {content: `文档上传中，已上传${(this.uploadProgress * 100).toFixed(0)}%`}),
        type: 'warning',
        icon: 'loading',
        duration: 0
      })
      return this.$axios.post(options.action, {
        ...options.data,
        file: options.file
      }, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        withCredentials: true,
        onUploadProgress: (e) => {
          if (e.progress === 1) {
            this.uploadDocMsg?.close()
            this.uploadProgress = 0
            this.uploadDocMsg = this.$message({
              message: `正在处理文档...`,
              type: 'warning',
              icon: 'loading',
              duration: 0
            })
          } else {
            this.uploadProgress = e.progress
          }
        },
      }).then(res => {
        this.uploadDoc(res)
      }).catch(err => {
        this.uploadDocMsg?.close()
        this.uploadDocMsg = null
        this.uploadProgress = 0
        return Promise.reject(err)
      })
    },
    onSelectFile(command) {
      if (command === 'a') {
        this.$refs.fileSelector.show()
      }
    },
    onFileSelected(uploadFile) {
      this.fileList = [uploadFile]
      this.doUpload()
    },
    doUpload() {
      this.$nextTick(() => {
        this.$refs.uploadRef.submit()
      })
    },
    showXYOffsetDisplacementDialog() {
      console.log('点击了勾差/框按钮', this.$refs.XYOffsetDisplacementDialog);
      if (this.$refs.XYOffsetDisplacementDialog && this.$refs.XYOffsetDisplacementDialog.show) {
        this.$refs.XYOffsetDisplacementDialog.show();
      } else {
        this.$message.error('弹窗组件未挂载或show方法不存在');
      }
    },
    handleXYOffsetDisplacementDialog(data) {
      // Handle the dialog submission
      console.log('XY Offset Displacement Dialog submitted with data:', data);
      this.XYOffsetData = data;
      this.refreshCropper();
      // 重新调用preview方法以更新字体大小
      this.preview();
    },
    unFreezeKeyBoard() {
      // 如果cropper组件有unFreezeKeyBoard方法则调用，否则忽略
      if (this.$refs.cropper && typeof this.$refs.cropper.unFreezeKeyBoard === 'function') {
        this.$refs.cropper.unFreezeKeyBoard();
      }
    }
  },
}
</script>


<style lang="scss" scoped>
::v-deep(.name-button.el-button--primary),
::v-deep(.total-score-button.el-button--primary),
::v-deep(.score-button.el-button--primary),
::v-deep(.student-number-button.el-button--primary) {
  background-color: #fff !important;
  border: 1px solid #1677FF !important;
  border-radius: 20px !important;
  color: #1677FF !important;
  box-shadow: none !important;
}

::v-deep(.name-button.el-button--success),
::v-deep(.total-score-button.el-button--success),
::v-deep(.score-button.el-button--success),
::v-deep(.student-number-button.el-button--success) {
  background-color: #1677FF !important;
  border: none !important;
  border-radius: 20px !important;
  color: #FFF !important;
  box-shadow: none !important;
}

// 统一按钮颜色为与"添加区域"按钮一致的 #1677FF
.header-actions {
  .header-action.el-button--primary {
    background-color: #1677FF !important;
    border-color: #1677FF !important;
    color: #FFF !important;
    transition: background-color 0.3s ease, border-color 0.3s ease;

    &:hover {
      background-color: #4a90ff !important;
      border-color: #4a90ff !important;
      color: #FFF !important; // 确保文字保持白色
    }
  }

  // 清空按钮保持白色
  .clear-button {
    background-color: #FFF !important;
    border: 1px solid #DCDFE6 !important;
    color: #606266 !important;
  }

  // 确保上传底卷按钮也是蓝色
  .el-upload .el-button--primary {
    background-color: #1677FF !important;
    border-color: #1677FF !important;
    color: #FFF !important;
    transition: background-color 0.3s ease, border-color 0.3s ease;

    &:hover {
      background-color: #4a90ff !important;
      border-color: #4a90ff !important;
      color: #FFF !important; // 确保文字保持白色
    }
  }
}

// 顺序画勾叉按钮样式
.right-actions {
  .el-button--primary {
    background-color: #1677FF !important;
    border-color: #1677FF !important;
    color: #FFF !important;
    transition: background-color 0.3s ease, border-color 0.3s ease;

    &:hover {
      background-color: #4a90ff !important;
      border-color: #4a90ff !important;
      color: #FFF !important; // 确保文字保持白色
    }
  }

  .el-button[text] {
    color: #1677FF !important;
    transition: color 0.3s ease;

    &:hover {
      color: #4a90ff !important;
    }
  }
}

.el-dialog__wrapper {
  z-index: 3000 !important;
}

.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  margin: 0 0 0 15px;

  .header-bar {
    margin-bottom: 20px;

    .icon {
      width: 28.28px;
      height: 22.89px;
      margin-right: 10px;
    }

    .header-title {
      font-weight: 700;
      font-size: 20px;
    }

    .header-preview {
      margin-left: 20px;

      :deep(.el-switch__label) {
        font-weight: 700;
        font-size: 15px;
      }
    }

    .filter-item {
      margin-right: 10px;
      width: 200px;
    }

    .header-actions {
      display: flex;
      align-items: center;

      .clear-button {
        margin-right: 10px;
        width: 84px;
        height: 32px;
        background: #FFFFFF;
        border-radius: 6px;
      }

      .header-action {
        margin-right: 10px;
      }

      .el-button + .el-button {
        margin-left: 0;
      }
    }
  }

  .main-content {
    height: calc(100% - 60px);
    display: flex;
    gap: 20px;

    .content-right {
      width: 50%;
      height: 100%;
      border: 1px solid var(--el-border-color);
      border-radius: 4px;
      box-shadow: var(--el-box-shadow);
    }

    .content-left {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 20px;

      .name-button {
        position: fixed;
        right: 280px;
        top: 160px;
        z-index: 999;
      }

      .student-number-button {
        position: fixed;
        right: 370px;
        top: 160px;
        z-index: 999;
      }

      .total-score-button {
        position: fixed;
        right: 460px;
        top: 160px;
        z-index: 999;
      }

      .score-button {
        position: fixed;
        right: 195px;
        top: 160px;
        z-index: 999;
      }

      .ocr-button {
        position: fixed;
        width: 104px;
        height: 32px;
        background-image: linear-gradient(90deg, #A45DFF 0%, #3772FF 100%);
        border-radius: 6px;
        bottom: 55px;
        right: 20%;
        border: none;
        z-index: 999;

        .text {
          font-weight: 400;
          font-size: 14px;
          color: #FFFFFF;
          text-align: center;
          line-height: 22px;
          margin-left: 5px;
          opacity: 0.7;
        }

        .icon {
          width: 16.4px;
          height: 16.4px;
        }
      }

      .config-panel {
        :deep(.el-card__body) {
          padding: 0 !important;
        }

        :deep(.el-collapse) {
          border-bottom: 0;
          border-top: 0;

          .el-collapse-item__header {
            display: none;
          }

          .el-collapse-item__wrap {
            border-bottom: 0;

            .el-collapse-item__content {
              padding-bottom: 0;
            }
          }
        }

        .card-header {
          display: flex;
          align-items: center;
          column-gap: 10px;
          justify-content: space-between;

          .right-actions {
            display: flex;
            align-items: center;
            column-gap: -5px;
          }

          .left-actions {
            display: flex;
            align-items: center;
            column-gap: 5px;
          }
        }

        .config-card {
          display: flex;
          flex-wrap: wrap;
          gap: 20px;

          .el-form-item {
            flex: 1 0 calc(50% - 10px);
            min-width: 300px;
            margin: 0;

            :deep(.el-form-item__content) {
              align-items: baseline;
            }
          }

          .full-width {
            flex: 1 0 100%;
          }
        }
      }

      .card-qsarea {
        flex: 1;
      }
    }
  }

  // 添加勾叉/框 位移/大小按钮的样式
  .icon-with-text {
    position: fixed;
    bottom: 20px;
    right: 130px;
    z-index: 999;
    display: flex;
    align-items: center;
    gap: 5px;
    white-space: nowrap;
    flex-shrink: 1;
    cursor: pointer;
    background-color: transparent;
    padding: 6px 12px;
    border-radius: 4px;
    border: none;
    font-size: 14px;
    color: #007bff;
  }

  .tooltip-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 180px;
    display: inline-block;
    font-size: 14px;
    color: #007bff;
    flex-shrink: 1;
  }

}
</style>